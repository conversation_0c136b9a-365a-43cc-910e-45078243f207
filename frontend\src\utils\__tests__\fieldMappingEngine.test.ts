/**
 * 字段映射引擎单元测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { FieldMappingEngine } from '../fieldMappingEngine'
import type { FieldMappingConfig } from '../../types/apiEnhanced'

// Mock配置数据
const mockConfig: FieldMappingConfig = {
  metadata: {
    version: '3.1.0',
    last_updated: '2025-07-27',
    description: '测试配置',
    author: '测试',
    tables: ['rule_template', 'rule_detail', 'rule_field_metadata']
  },
  field_definitions: {
    common_fields: {
      rule_id: {
        chinese_name: '规则ID',
        data_type: 'string',
        required: false,
        max_length: 100,
        description: '规则的唯一标识符',
        database_column: 'rule_id',
        api_field: 'rule_id',
        excel_column: '规则ID'
      },
      rule_name: {
        chinese_name: '规则名称',
        data_type: 'string',
        required: true,
        max_length: 500,
        description: '规则的显示名称',
        database_column: 'rule_name',
        api_field: 'rule_name',
        excel_column: '规则名称',
        validation_rules: ['required', 'max_length:500']
      },
      level1: {
        chinese_name: '一级错误类型',
        data_type: 'string',
        required: true,
        max_length: 100,
        description: '一级错误类型',
        database_column: 'level1',
        api_field: 'level1',
        excel_column: '一级错误类型',
        validation_rules: ['required', 'max_length:100']
      },
      age_threshold: {
        chinese_name: '年龄阈值',
        data_type: 'integer',
        required: false,
        min_value: 0,
        max_value: 150,
        description: '年龄阈值',
        database_column: 'age_threshold',
        api_field: 'age_threshold',
        excel_column: '年龄阈值',
        validation_rules: ['integer', 'min:0', 'max:150']
      },
      is_active: {
        chinese_name: '是否激活',
        data_type: 'boolean',
        required: false,
        description: '是否激活',
        database_column: 'is_active',
        api_field: 'is_active',
        excel_column: '是否激活'
      },
      tags: {
        chinese_name: '标签',
        data_type: 'array',
        required: false,
        description: '标签列表',
        database_column: 'tags',
        api_field: 'tags',
        excel_column: '标签'
      }
    },
    specific_fields: {
      custom_field: {
        chinese_name: '自定义字段',
        data_type: 'string',
        required: false,
        description: '自定义字段',
        database_column: 'custom_field',
        api_field: 'custom_field',
        excel_column: '自定义字段'
      }
    }
  }
}

// Mock fetch
;(globalThis as any).fetch = vi.fn()

describe('FieldMappingEngine', () => {
  let engine: FieldMappingEngine

  beforeEach(() => {
    engine = new FieldMappingEngine()
    vi.clearAllMocks()
    
    // Mock fetch返回配置数据
    ;((globalThis as any).fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockConfig)
    })
  })

  describe('初始化', () => {
    it('应该成功初始化', async () => {
      await engine.initialize()
      expect((globalThis as any).fetch).toHaveBeenCalledWith('/data/field_mapping.json')
    })

    it('初始化失败时应该抛出错误', async () => {
      ;((globalThis as any).fetch as any).mockResolvedValue({
        ok: false,
        statusText: 'Not Found'
      })

      await expect(engine.initialize()).rejects.toThrow('Failed to load field mapping config: Not Found')
    })

    it('未初始化时调用方法应该抛出错误', () => {
      expect(() => engine.getFieldDefinition('rule_id')).toThrow('FieldMappingEngine not initialized')
    })
  })

  describe('字段定义获取', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    it('应该获取通用字段定义', () => {
      const definition = engine.getFieldDefinition('rule_id')
      expect(definition).toEqual(mockConfig.field_definitions.common_fields.rule_id)
    })

    it('应该获取特定字段定义', () => {
      const definition = engine.getFieldDefinition('custom_field')
      expect(definition).toEqual(mockConfig.field_definitions.specific_fields!.custom_field)
    })

    it('不存在的字段应该返回null', () => {
      const definition = engine.getFieldDefinition('non_existent_field')
      expect(definition).toBeNull()
    })

    it('应该缓存字段定义', () => {
      // 第一次调用
      const definition1 = engine.getFieldDefinition('rule_id')
      // 第二次调用应该从缓存获取
      const definition2 = engine.getFieldDefinition('rule_id')
      
      expect(definition1).toBe(definition2)
      
      const stats = engine.getCacheStats()
      expect(stats.size).toBeGreaterThan(0)
    })
  })

  describe('字段属性获取', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    it('应该获取字段中文名称', () => {
      expect(engine.getFieldChineseName('rule_id')).toBe('规则ID')
      expect(engine.getFieldChineseName('non_existent')).toBe('non_existent')
    })

    it('应该获取字段数据类型', () => {
      expect(engine.getFieldDataType('rule_id')).toBe('string')
      expect(engine.getFieldDataType('age_threshold')).toBe('integer')
      expect(engine.getFieldDataType('non_existent')).toBe('string')
    })

    it('应该检查字段是否必填', () => {
      expect(engine.isFieldRequired('rule_name')).toBe(true)
      expect(engine.isFieldRequired('rule_id')).toBe(false)
      expect(engine.isFieldRequired('non_existent')).toBe(false)
    })

    it('应该获取字段验证规则', () => {
      expect(engine.getFieldValidationRules('rule_name')).toEqual(['required', 'max_length:500'])
      expect(engine.getFieldValidationRules('rule_id')).toEqual([])
    })
  })

  describe('数据转换', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    it('应该转换请求数据', () => {
      const inputData = {
        rule_id: 'test-001',
        rule_name: '测试规则',
        age_threshold: '18',
        is_active: 'true',
        tags: 'tag1,tag2,tag3'
      }

      const transformed = engine.transformRequestData(inputData)
      
      expect(transformed).toEqual({
        rule_id: 'test-001',
        rule_name: '测试规则',
        age_threshold: 18,
        is_active: true,
        tags: ['tag1', 'tag2', 'tag3']
      })
    })

    it('应该转换响应数据', () => {
      const responseData = {
        rule_id: 'test-001',
        rule_name: '测试规则',
        age_threshold: 18,
        is_active: true,
        tags: ['tag1', 'tag2']
      }

      const transformed = engine.transformResponseData(responseData)
      
      expect(transformed).toEqual(responseData)
    })

    it('应该处理未定义的字段', () => {
      const inputData = {
        rule_id: 'test-001',
        unknown_field: 'unknown_value'
      }

      const transformed = engine.transformRequestData(inputData)
      
      expect(transformed).toEqual({
        rule_id: 'test-001',
        unknown_field: 'unknown_value'
      })
    })
  })

  describe('数据验证', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    it('应该验证有效数据', () => {
      const validData = {
        rule_name: '测试规则',
        level1: '错误类型',
        age_threshold: 25
      }

      const result = engine.validateFields(validData)
      
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该检测必填字段缺失', () => {
      const invalidData = {
        rule_id: 'test-001'
        // 缺少必填字段 rule_name
      }

      const result = engine.validateFields(invalidData)
      
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('规则名称不能为空')
    })

    it('应该检测字段长度超限', () => {
      const invalidData = {
        rule_name: 'a'.repeat(501), // 超过最大长度500
        level1: '错误类型'
      }

      const result = engine.validateFields(invalidData)
      
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('规则名称长度不能超过500个字符')
    })

    it('应该检测数据类型错误', () => {
      const invalidData = {
        rule_name: '测试规则',
        level1: '错误类型',
        age_threshold: 'not_a_number'
      }

      const result = engine.validateFields(invalidData)
      
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('年龄阈值必须为整数')
    })

    it('应该检测数值范围错误', () => {
      const invalidData = {
        rule_name: '测试规则',
        level1: '错误类型',
        age_threshold: 200 // 超过最大值150
      }

      const result = engine.validateFields(invalidData)
      
      expect(result.valid).toBe(false)
      expect(result.errors).toContain('年龄阈值不能大于150')
    })

    it('应该处理未知字段警告', () => {
      const dataWithUnknownField = {
        rule_name: '测试规则',
        level1: '错误类型',
        unknown_field: 'unknown_value'
      }

      const result = engine.validateFields(dataWithUnknownField)
      
      expect(result.warnings).toContain('未知字段: unknown_field')
    })
  })

  describe('缓存管理', () => {
    beforeEach(async () => {
      await engine.initialize()
    })

    it('应该提供缓存统计信息', () => {
      // 触发一些缓存操作
      engine.getFieldDefinition('rule_id')
      engine.getFieldDefinition('rule_name')
      
      const stats = engine.getCacheStats()
      
      expect(stats.size).toBeGreaterThan(0)
      expect(stats.keys).toContain('field_def_rule_id')
      expect(stats.keys).toContain('field_def_rule_name')
    })

    it('应该能够清除缓存', () => {
      // 添加一些缓存
      engine.getFieldDefinition('rule_id')
      expect(engine.getCacheStats().size).toBeGreaterThan(0)
      
      // 清除缓存
      engine.clearCache()
      expect(engine.getCacheStats().size).toBe(0)
    })
  })
})
