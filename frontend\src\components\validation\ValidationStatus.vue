<template>
  <div class="validation-status">
    <!-- 整体状态指示器 -->
    <div class="validation-status__header">
      <div class="status-indicator">
        <el-icon 
          :class="statusIconClass"
          class="status-icon"
        >
          <component :is="statusIcon" />
        </el-icon>
        <span class="status-text">{{ statusText }}</span>
      </div>
      
      <!-- 统计信息 -->
      <div class="status-stats">
        <el-tag 
          v-if="errorCount > 0" 
          type="danger" 
          size="small"
          class="stat-tag"
        >
          {{ errorCount }} 个错误
        </el-tag>
        <el-tag 
          v-if="warningCount > 0" 
          type="warning" 
          size="small"
          class="stat-tag"
        >
          {{ warningCount }} 个警告
        </el-tag>
        <el-tag 
          v-if="validFieldCount > 0" 
          type="success" 
          size="small"
          class="stat-tag"
        >
          {{ validFieldCount }} 个通过
        </el-tag>
      </div>
    </div>

    <!-- 详细字段状态 -->
    <div 
      v-if="showDetails && fieldStates && Object.keys(fieldStates).length > 0" 
      class="validation-status__details"
    >
      <div class="details-header">
        <span class="details-title">字段校验状态</span>
        <el-button 
          text 
          size="small" 
          @click="toggleDetails"
        >
          {{ detailsExpanded ? '收起' : '展开' }}
          <el-icon><component :is="detailsExpanded ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
        </el-button>
      </div>
      
      <el-collapse-transition>
        <div v-show="detailsExpanded" class="field-states">
          <div 
            v-for="[fieldName, state] in Object.entries(fieldStates)" 
            :key="fieldName"
            class="field-state-item"
          >
            <div class="field-info">
              <div class="field-name">
                <el-icon :class="getFieldStatusClass(state)">
                  <component :is="getFieldStatusIcon(state)" />
                </el-icon>
                <span class="field-label">{{ getFieldChineseName(fieldName) }}</span>
                <span class="field-name-en">({{ fieldName }})</span>
              </div>
              
              <div class="field-meta">
                <span v-if="state.is_validating" class="validating-text">
                  <el-icon class="loading"><Loading /></el-icon>
                  校验中...
                </span>
                <span v-else-if="state.last_validation_time" class="validation-time">
                  {{ formatValidationTime(state.last_validation_time) }}
                </span>
                <span v-if="state.validation_duration" class="validation-duration">
                  ({{ state.validation_duration.toFixed(1) }}ms)
                </span>
              </div>
            </div>
            
            <!-- 字段错误信息 -->
            <div
              v-if="state.has_errors && fieldErrors && fieldErrors[fieldName]"
              class="field-errors"
            >
              <ValidationMessage
                v-for="(error, index) in fieldErrors[fieldName] || []"
                :key="index"
                type="error"
                :message="error.error_message"
                :suggestions="error.suggestions"
                :error-details="error"
                :show-details="false"
                class="field-error-message"
              />
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <!-- 性能指标（开发模式） -->
    <div 
      v-if="showPerformance && performanceMetrics" 
      class="validation-status__performance"
    >
      <div class="performance-header">
        <span class="performance-title">性能指标</span>
      </div>
      <div class="performance-metrics">
        <div class="metric-item">
          <span class="metric-label">总校验次数：</span>
          <span class="metric-value">{{ performanceMetrics.total_validations }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">平均耗时：</span>
          <span class="metric-value">{{ performanceMetrics.average_duration.toFixed(1) }}ms</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">缓存命中率：</span>
          <span class="metric-value">{{ (performanceMetrics.cache_hit_rate * 100).toFixed(1) }}%</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">错误率：</span>
          <span class="metric-value">{{ (performanceMetrics.error_rate * 100).toFixed(1) }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import {
  CircleCheckFilled,
  WarningFilled,
  Loading,
  InfoFilled
} from '@element-plus/icons-vue'
import ValidationMessage from './ValidationMessage.vue'
import type { 
  ValidationState, 
  ValidationError,
  ValidationPerformanceMetrics 
} from '../../utils/validation/validationTypes'

export interface ValidationStatusProps {
  // 整体状态
  isValid?: boolean
  isValidating?: boolean
  errorCount?: number
  warningCount?: number
  validFieldCount?: number
  
  // 字段状态
  fieldStates?: Record<string, ValidationState>
  fieldErrors?: Record<string, ValidationError[]>
  
  // 性能指标
  performanceMetrics?: ValidationPerformanceMetrics
  
  // 显示选项
  showDetails?: boolean
  showPerformance?: boolean
  
  // 字段名称映射
  fieldNameMap?: Record<string, string>
}

const props = withDefaults(defineProps<ValidationStatusProps>(), {
  isValid: true,
  isValidating: false,
  errorCount: 0,
  warningCount: 0,
  validFieldCount: 0,
  showDetails: true,
  showPerformance: false
})

// 详情展开状态
const detailsExpanded = ref(false)

// 整体状态
const statusIcon = computed(() => {
  if (props.isValidating) return Loading
  if (!props.isValid) return WarningFilled
  if (props.warningCount > 0) return InfoFilled
  return CircleCheckFilled
})

const statusIconClass = computed(() => ({
  'status-icon--validating': props.isValidating,
  'status-icon--error': !props.isValid && !props.isValidating,
  'status-icon--warning': props.isValid && props.warningCount > 0,
  'status-icon--success': props.isValid && props.warningCount === 0 && !props.isValidating
}))

const statusText = computed(() => {
  if (props.isValidating) return '校验中...'
  if (!props.isValid) return '校验失败'
  if (props.warningCount > 0) return '校验通过（有警告）'
  return '校验通过'
})

// 字段状态图标
const getFieldStatusIcon = (state: ValidationState) => {
  if (state.is_validating) return Loading
  if (state.has_errors) return WarningFilled
  if (state.warning_count > 0) return InfoFilled
  return CircleCheckFilled
}

const getFieldStatusClass = (state: ValidationState) => ({
  'field-icon--validating': state.is_validating,
  'field-icon--error': state.has_errors,
  'field-icon--warning': !state.has_errors && state.warning_count > 0,
  'field-icon--success': !state.has_errors && state.warning_count === 0
})

// 获取字段中文名称
const getFieldChineseName = (fieldName: string): string => {
  return props.fieldNameMap?.[fieldName] || fieldName
}

// 格式化校验时间
const formatValidationTime = (time: Date): string => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  
  if (diff < 1000) return '刚刚'
  if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  return time.toLocaleTimeString()
}

// 切换详情展开状态
const toggleDetails = () => {
  detailsExpanded.value = !detailsExpanded.value
}
</script>

<style scoped>
.validation-status {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #ffffff;
  overflow: hidden;
}

.validation-status__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-icon--validating {
  color: #409eff;
  animation: spin 1s linear infinite;
}

.status-icon--error {
  color: #f56c6c;
}

.status-icon--warning {
  color: #e6a23c;
}

.status-icon--success {
  color: #67c23a;
}

.status-text {
  font-weight: 500;
  color: #303133;
}

.status-stats {
  display: flex;
  gap: 8px;
}

.stat-tag {
  font-size: 12px;
}

.validation-status__details {
  border-bottom: 1px solid #e4e7ed;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
}

.details-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.field-states {
  padding: 0;
}

.field-state-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.field-state-item:last-child {
  border-bottom: none;
}

.field-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.field-name {
  display: flex;
  align-items: center;
  gap: 6px;
}

.field-icon--validating {
  color: #409eff;
  animation: spin 1s linear infinite;
}

.field-icon--error {
  color: #f56c6c;
}

.field-icon--warning {
  color: #e6a23c;
}

.field-icon--success {
  color: #67c23a;
}

.field-label {
  font-weight: 500;
  color: #303133;
}

.field-name-en {
  font-size: 12px;
  color: #909399;
}

.field-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.validating-text {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
}

.loading {
  animation: spin 1s linear infinite;
}

.field-errors {
  margin-top: 8px;
}

.field-error-message {
  margin-bottom: 4px;
}

.validation-status__performance {
  padding: 12px 16px;
  background-color: #f8f9fa;
}

.performance-header {
  margin-bottom: 8px;
}

.performance-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.metric-label {
  color: #909399;
}

.metric-value {
  color: #303133;
  font-weight: 500;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .validation-status__header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .status-stats {
    align-self: stretch;
    justify-content: flex-start;
  }
  
  .field-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
  }
}
</style>
