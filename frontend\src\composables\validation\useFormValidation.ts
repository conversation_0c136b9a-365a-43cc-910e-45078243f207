/**
 * 表单校验组合式函数
 * 专门用于Element Plus表单组件的校验集成
 */

import { ref, computed, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage } from 'element-plus'
import { useValidation, UseValidationOptions } from './useValidation'
import {
  ValidationResult
} from '../../utils/validation/validationTypes'

export interface UseFormValidationOptions extends UseValidationOptions {
  formRef?: FormInstance
  submitOnValid?: boolean
  resetOnSubmit?: boolean
}

export function useFormValidation(options: UseFormValidationOptions) {
  // 继承基础校验功能
  const baseValidation = useValidation(options)
  
  // 表单相关状态
  const formRef = ref<FormInstance>()
  const isSubmitting = ref(false)
  const submitAttempts = ref(0)
  const lastSubmitTime = ref<Date>()

  // Element Plus 表单规则
  const elementFormRules = computed<FormRules>(() => {
    const rules: FormRules = {}
    
    for (const [fieldName, fieldRules] of Object.entries(baseValidation.formRules.value)) {
      rules[fieldName] = fieldRules
    }
    
    return rules
  })

  // 表单状态
  const formState = computed(() => ({
    isValid: baseValidation.isValid.value,
    hasErrors: baseValidation.hasErrors.value,
    hasWarnings: baseValidation.hasWarnings.value,
    errorCount: baseValidation.errorCount.value,
    warningCount: baseValidation.warningCount.value,
    isValidating: baseValidation.isValidating.value,
    isSubmitting: isSubmitting.value,
    canSubmit: baseValidation.isValid.value && !isSubmitting.value && !baseValidation.isValidating.value
  }))

  /**
   * 设置表单引用
   */
  const setFormRef = (ref: FormInstance) => {
    formRef.value = ref
  }

  /**
   * 校验表单（Element Plus方式）
   */
  const validateElementForm = async (): Promise<boolean> => {
    if (!formRef.value) {
      console.warn('表单引用未设置')
      return false
    }

    try {
      await formRef.value.validate()
      return true
    } catch (error) {
      console.log('Element Plus表单校验失败:', error)
      return false
    }
  }

  /**
   * 校验表单（自定义校验引擎）
   */
  const validateCustomForm = async (formData: Record<string, any>): Promise<ValidationResult> => {
    return await baseValidation.validateForm(formData)
  }

  /**
   * 综合校验表单
   */
  const validateForm = async (formData: Record<string, any>): Promise<{
    elementValid: boolean
    customValid: boolean
    customResult: ValidationResult
    overallValid: boolean
  }> => {
    baseValidation.isValidating.value = true

    try {
      // 并行执行两种校验
      const [elementValid, customResult] = await Promise.all([
        validateElementForm(),
        validateCustomForm(formData)
      ])

      const overallValid = elementValid && customResult.valid

      return {
        elementValid,
        customValid: customResult.valid,
        customResult,
        overallValid
      }
    } finally {
      baseValidation.isValidating.value = false
    }
  }

  /**
   * 校验单个字段（Element Plus方式）
   */
  const validateElementField = async (fieldName: string): Promise<boolean> => {
    if (!formRef.value) {
      console.warn('表单引用未设置')
      return false
    }

    try {
      await formRef.value.validateField(fieldName)
      return true
    } catch (error) {
      console.log(`字段 ${fieldName} Element Plus校验失败:`, error)
      return false
    }
  }

  /**
   * 综合校验单个字段
   */
  const validateField = async (fieldName: string, value: any): Promise<{
    elementValid: boolean
    customValid: boolean
    customResult: ValidationResult
    overallValid: boolean
  }> => {
    // 并行执行两种校验
    const [elementValid, customResult] = await Promise.all([
      validateElementField(fieldName),
      baseValidation.validateField(fieldName, value)
    ])

    const overallValid = elementValid && customResult.valid

    return {
      elementValid,
      customValid: customResult.valid,
      customResult,
      overallValid
    }
  }

  /**
   * 提交表单
   */
  const submitForm = async (
    formData: Record<string, any>,
    submitHandler?: (data: Record<string, any>) => Promise<any>
  ): Promise<any> => {
    if (isSubmitting.value) {
      console.warn('表单正在提交中')
      return
    }

    submitAttempts.value++
    isSubmitting.value = true
    lastSubmitTime.value = new Date()

    try {
      // 校验表单
      const validationResult = await validateForm(formData)
      
      if (!validationResult.overallValid) {
        ElMessage.error('表单校验失败，请检查输入内容')
        
        // 聚焦到第一个错误字段
        await nextTick()
        focusFirstErrorField()
        
        return { success: false, errors: validationResult.customResult.errors }
      }

      // 执行提交处理器
      let result
      if (submitHandler) {
        result = await submitHandler(formData)
      }

      // 提交成功处理
      if (options.resetOnSubmit) {
        resetForm()
      }

      ElMessage.success('提交成功')
      return { success: true, data: result }

    } catch (error) {
      console.error('表单提交失败:', error)
      ElMessage.error('提交失败，请重试')
      return { success: false, error }
    } finally {
      isSubmitting.value = false
    }
  }

  /**
   * 聚焦到第一个错误字段
   */
  const focusFirstErrorField = () => {
    if (!formRef.value) return

    const firstError = baseValidation.validationErrors.value[0]
    if (firstError) {
      const fieldElement = document.querySelector(`[name="${firstError.field_name}"]`) as HTMLElement
      if (fieldElement) {
        fieldElement.focus()
      }
    }
  }

  /**
   * 重置表单
   */
  const resetForm = () => {
    if (formRef.value) {
      formRef.value.resetFields()
    }
    baseValidation.clearValidation()
    submitAttempts.value = 0
    lastSubmitTime.value = undefined
  }

  /**
   * 清除表单校验
   */
  const clearFormValidation = () => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
    baseValidation.clearValidation()
  }

  /**
   * 清除字段校验
   */
  const clearFieldValidation = (fieldName: string) => {
    if (formRef.value) {
      formRef.value.clearValidate(fieldName)
    }
    baseValidation.clearFieldValidation(fieldName)
  }

  /**
   * 获取表单数据
   */
  const getFormData = (): Record<string, any> => {
    if (!formRef.value) return {}
    
    // 这里需要根据实际表单结构获取数据
    // 通常从表单模型中获取
    return {}
  }

  /**
   * 设置表单数据
   */
  const setFormData = (data: Record<string, any>) => {
    if (!formRef.value) return
    
    // 设置表单字段值
    for (const [_fieldName, _value] of Object.entries(data)) {
      // 这里需要根据实际表单结构设置数据
      // 通常设置到表单模型中
    }
  }

  /**
   * 获取字段校验状态
   */
  const getFieldValidationStatus = (fieldName: string) => {
    const fieldState = baseValidation.fieldStates[fieldName]
    const fieldErrors = baseValidation.getFieldErrors(fieldName)
    
    return {
      isValidating: fieldState?.is_validating || false,
      hasErrors: fieldState?.has_errors || false,
      errorCount: fieldState?.error_count || 0,
      warningCount: fieldState?.warning_count || 0,
      errors: fieldErrors,
      isValid: !fieldState?.has_errors
    }
  }

  /**
   * 批量设置字段值并校验
   */
  const setFieldsAndValidate = async (fields: Record<string, any>) => {
    const results: Record<string, any> = {}
    
    for (const [fieldName, value] of Object.entries(fields)) {
      try {
        const result = await validateField(fieldName, value)
        results[fieldName] = result
      } catch (error) {
        console.error(`设置字段 ${fieldName} 失败:`, error)
        results[fieldName] = { error }
      }
    }
    
    return results
  }

  /**
   * 获取表单校验摘要
   */
  const getValidationSummary = () => {
    return {
      totalFields: Object.keys(baseValidation.fieldStates).length,
      validFields: Object.values(baseValidation.fieldStates).filter(s => !s.has_errors).length,
      errorFields: Object.values(baseValidation.fieldStates).filter(s => s.has_errors).length,
      totalErrors: baseValidation.errorCount.value,
      totalWarnings: baseValidation.warningCount.value,
      isValid: baseValidation.isValid.value,
      submitAttempts: submitAttempts.value,
      lastSubmitTime: lastSubmitTime.value
    }
  }

  return {
    // 继承基础校验功能
    ...baseValidation,
    
    // 表单特定状态
    formRef,
    isSubmitting,
    submitAttempts,
    lastSubmitTime,
    elementFormRules,
    formState,
    
    // 表单特定方法
    setFormRef,
    validateElementForm,
    validateCustomForm,
    validateForm,
    validateElementField,
    validateField,
    submitForm,
    resetForm,
    clearFormValidation,
    clearFieldValidation,
    getFormData,
    setFormData,
    getFieldValidationStatus,
    setFieldsAndValidate,
    getValidationSummary,
    focusFirstErrorField
  }
}
