/**
 * 规则详情Store状态管理测试
 * 测试增强的状态管理功能、计算属性和异步处理
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useRuleDetailsStore } from '../ruleDetails.js'

// Mock 依赖
vi.mock('@/composables/core/useAsyncState', () => ({
  useAsyncState: vi.fn(() => ({
    isLoading: { value: false },
    execute: vi.fn(),
    clearCache: vi.fn()
  }))
}))

vi.mock('@/composables/core/useStateMachine', () => ({
  useStateMachine: vi.fn(() => ({
    start: vi.fn(),
    success: vi.fn(),
    error: vi.fn()
  }))
}))

vi.mock('@/composables/core/useFeedback', () => ({
  useFeedback: vi.fn(() => ({
    toastSuccess: vi.fn(),
    toastError: vi.fn(),
    toastInfo: vi.fn()
  }))
}))

vi.mock('../app', () => ({
  useAppStore: vi.fn(() => ({
    addLoadingTask: vi.fn(),
    removeLoadingTask: vi.fn(),
    setError: vi.fn()
  }))
}))

vi.mock('@/api/enhancedRuleDetails', () => ({
  enhancedRuleDetailsApi: {
    initialize: vi.fn().mockResolvedValue(true),
    getRuleDetailsList: vi.fn(),
    getRuleDetailById: vi.fn(),
    createRuleDetail: vi.fn(),
    updateRuleDetail: vi.fn(),
    deleteRuleDetail: vi.fn(),
    getCacheStats: vi.fn().mockResolvedValue({ size: 10 }),
    clearCache: vi.fn()
  }
}))

vi.mock('@/utils/enhancedErrorHandler', () => ({
  enhancedErrorHandler: {
    handle: vi.fn()
  }
}))

describe('RuleDetailsStore 状态管理', () => {
  let store

  beforeEach(() => {
    setActivePinia(createPinia())
    store = useRuleDetailsStore()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('基础状态', () => {
    it('应该初始化默认状态', () => {
      expect(store.detailsList).toEqual([])
      expect(store.currentDetail).toBeNull()
      expect(store.selectedDetails).toEqual([])
      expect(store.pagination.page).toBe(1)
      expect(store.pagination.pageSize).toBe(20)
      expect(store.loading).toBe(false)
    })

    it('应该正确设置操作状态', () => {
      expect(store.operationStatus.lastOperation).toBeNull()
      expect(store.operationStatus.retryCount).toBe(0)
      expect(store.operationStatus.maxRetries).toBe(3)
      expect(store.operationStatus.isRecovering).toBe(false)
      expect(store.operationStatus.errors).toEqual([])
    })
  })

  describe('增强计算属性', () => {
    it('应该正确计算基础统计', () => {
      // 设置测试数据
      store.detailsList = [
        { id: 1, rule_key: 'test1' },
        { id: 2, rule_key: 'test2' }
      ]
      store.selectedDetails = [{ id: 1, rule_key: 'test1' }]

      expect(store.detailsCount).toBe(2)
      expect(store.hasDetails).toBe(true)
      expect(store.selectedCount).toBe(1)
      expect(store.hasSelected).toBe(true)
    })

    it('应该正确计算加载状态', () => {
      store.loading = true
      expect(store.isLoading).toBe(true)
      expect(store.loadingState.isLoading).toBe(true)
      expect(store.loadingState.loadingType).toBe('list')
      expect(store.loadingState.canOperate).toBe(false)

      store.loading = false
      store.detailLoading = true
      expect(store.loadingState.loadingType).toBe('detail')
    })

    it('应该正确计算分页信息', () => {
      store.pagination = {
        page: 2,
        pageSize: 10,
        total: 25,
        hasNext: true,
        hasPrev: true
      }

      const paginationInfo = store.paginationInfo
      expect(paginationInfo.current).toBe(2)
      expect(paginationInfo.total).toBe(25)
      expect(paginationInfo.pageSize).toBe(10)
      expect(paginationInfo.totalPages).toBe(3)
      expect(paginationInfo.hasNext).toBe(true)
      expect(paginationInfo.hasPrev).toBe(true)
    })

    it('应该正确计算过滤器状态', () => {
      store.filters = {
        status: 'ACTIVE',
        search: 'test',
        errorLevel1: 'ERROR',
        ruleCategory: 'VALIDATION'
      }

      const activeFilters = store.activeFilters
      expect(activeFilters).toHaveLength(4)
      expect(activeFilters).toContain('状态: ACTIVE')
      expect(activeFilters).toContain('搜索: test')
      expect(store.hasActiveFilters).toBe(true)
    })

    it('应该正确计算操作状态摘要', () => {
      store.operationStatus = {
        retryCount: 1,
        maxRetries: 3,
        errors: ['error1'],
        isRecovering: false,
        lastSuccessTime: Date.now(),
        operationHistory: [
          { success: true },
          { success: false },
          { success: true }
        ]
      }

      const summary = store.operationSummary
      expect(summary.canRetry).toBe(true)
      expect(summary.hasErrors).toBe(true)
      expect(summary.isHealthy).toBe(false)
      expect(summary.lastOperationSuccess).toBe(true)
      expect(summary.operationRate).toBeCloseTo(0.67, 2)
    })

    it('应该正确计算数据质量指标', () => {
      // 无数据时
      store.detailsList = []
      expect(store.dataQuality.score).toBe(100)
      expect(store.dataQuality.issues).toEqual([])

      // 有重复数据时
      store.detailsList = [
        { id: 1, rule_key: 'test1' },
        { id: 1, rule_key: 'test1' }, // 重复
        { id: 2, rule_key: 'test2' }
      ]

      const quality = store.dataQuality
      expect(quality.score).toBe(90) // 100 - (1 * 10)
      expect(quality.issues).toHaveLength(1)
      expect(quality.issues[0]).toContain('发现 1 条重复数据')
    })
  })

  describe('缓存管理', () => {
    it('应该正确初始化缓存管理器', () => {
      expect(store.cacheManager.lastClearTime).toBeDefined()
      expect(store.cacheManager.hitCount).toBe(0)
      expect(store.cacheManager.missCount).toBe(0)
      expect(store.cacheManager.autoCleanup).toBe(true)
      expect(store.cacheManager.maxAge).toBe(5 * 60 * 1000)
      expect(store.cacheManager.maxSize).toBe(100)
    })

    it('应该能够清理过期缓存', async () => {
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetails')
      
      // 模拟缓存过大
      enhancedRuleDetailsApi.getCacheStats.mockResolvedValue({ size: 150 })
      
      await store.clearStaleCache()
      
      expect(enhancedRuleDetailsApi.getCacheStats).toHaveBeenCalled()
      expect(enhancedRuleDetailsApi.clearCache).toHaveBeenCalled()
      expect(store.cacheManager.lastClearTime).toBeDefined()
    })
  })

  describe('错误处理', () => {
    it('应该正确处理操作错误', async () => {
      const error = new Error('测试错误')
      
      await store.handleOperationError(error)
      
      expect(store.operationStatus.isRecovering).toBe(false)
      expect(store.operationStatus.operationHistory).toHaveLength(1)
      expect(store.operationStatus.operationHistory[0].success).toBe(false)
      expect(store.operationStatus.operationHistory[0].error).toBe('测试错误')
    })

    it('应该支持重试机制', async () => {
      const error = new Error('网络错误')
      store.operationStatus.retryCount = 0
      store.operationStatus.maxRetries = 3
      
      await store.handleOperationError(error)
      
      expect(store.operationStatus.retryCount).toBe(1)
    })

    it('应该在达到最大重试次数时停止重试', async () => {
      const error = new Error('持续错误')
      store.operationStatus.retryCount = 3
      store.operationStatus.maxRetries = 3
      
      await store.handleOperationError(error)
      
      expect(store.operationStatus.retryCount).toBe(3)
    })
  })

  describe('状态重置', () => {
    it('应该正确重置所有状态', async () => {
      // 设置一些状态
      store.detailsList = [{ id: 1 }]
      store.currentDetail = { id: 1 }
      store.selectedDetails = [{ id: 1 }]
      store.loading = true
      store.operationStatus.errors = ['error']

      await store.resetStore()

      expect(store.detailsList).toEqual([])
      expect(store.currentDetail).toBeNull()
      expect(store.selectedDetails).toEqual([])
      expect(store.loading).toBe(false)
      expect(store.operationStatus.errors).toEqual([])
      expect(store.pagination.page).toBe(1)
    })
  })

  describe('状态监听', () => {
    it('应该监听操作状态变化', () => {
      const initialErrorCount = store.operationStatus.errors.length
      
      // 添加新错误
      store.operationStatus.errors.push({
        timestamp: Date.now(),
        operation: '测试操作',
        message: '测试错误',
        code: 'TEST_ERROR'
      })

      // 验证错误被添加
      expect(store.operationStatus.errors.length).toBe(initialErrorCount + 1)
    })
  })
})
