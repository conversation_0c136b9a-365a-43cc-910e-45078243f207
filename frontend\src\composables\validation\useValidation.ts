/**
 * 校验组合式函数
 * 提供统一的Vue校验接口
 */

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  ValidationResult, 
  ValidationState, 
  ElementFormRule,
  ValidationEvent,
  RealTimeValidationOptions
} from '../../utils/validation/validationTypes'
import { DynamicValidationEngine } from '../../utils/validation/dynamicValidationEngine'
import { validationRuleSync } from '../../utils/validation/validationRuleSync'
import { RealTimeValidator } from '../../utils/validation/realTimeValidator'

export interface UseValidationOptions {
  ruleKey: string
  realTimeOptions?: Partial<RealTimeValidationOptions>
  autoSync?: boolean
  showMessages?: boolean
}

export function useValidation(options: UseValidationOptions) {
  const validationEngine = new DynamicValidationEngine()
  const realTimeValidator = new RealTimeValidator(options.realTimeOptions)
  
  // 响应式状态
  const isValidating = ref(false)
  const validationErrors = ref<ValidationResult['errors']>([])
  const validationWarnings = ref<string[]>([])
  const fieldStates = reactive<Record<string, ValidationState>>({})
  const formRules = ref<Record<string, ElementFormRule[]>>({})

  // 计算属性
  const hasErrors = computed(() => validationErrors.value.length > 0)
  const hasWarnings = computed(() => validationWarnings.value.length > 0)
  const isValid = computed(() => !hasErrors.value)
  const errorCount = computed(() => validationErrors.value.length)
  const warningCount = computed(() => validationWarnings.value.length)

  /**
   * 初始化校验配置
   */
  const initializeValidation = async () => {
    try {
      // 同步校验规则
      if (options.autoSync !== false) {
        await validationRuleSync.syncRuleConfig(options.ruleKey)
      }

      // 获取Element Plus表单规则
      const ruleConfig = validationRuleSync.getRuleConfig(options.ruleKey)
      if (ruleConfig) {
        const elementRules: Record<string, ElementFormRule[]> = {}
        
        for (const [fieldName, fieldConfig] of Object.entries(ruleConfig.fields)) {
          elementRules[fieldName] = fieldConfig.element_rules
        }
        
        formRules.value = elementRules
      }

      console.log(`校验配置初始化完成: ${options.ruleKey}`)
    } catch (error) {
      console.error('校验配置初始化失败:', error)
      if (options.showMessages !== false) {
        ElMessage.error('校验配置加载失败')
      }
    }
  }

  /**
   * 校验单个字段
   */
  const validateField = async (fieldName: string, value: any): Promise<ValidationResult> => {
    try {
      isValidating.value = true
      
      const result = await validationEngine.validateField(fieldName, value, options.ruleKey)
      
      // 更新字段状态
      fieldStates[fieldName] = {
        is_validating: false,
        has_errors: !result.valid,
        error_count: result.errors.length,
        warning_count: result.warnings.length,
        last_validation_time: new Date(),
        validation_duration: result.duration
      }

      // 更新全局错误状态
      updateGlobalErrors()

      return result
    } catch (error) {
      console.error(`字段 ${fieldName} 校验失败:`, error)
      throw error
    } finally {
      isValidating.value = false
    }
  }

  /**
   * 校验整个表单
   */
  const validateForm = async (formData: Record<string, any>): Promise<ValidationResult> => {
    try {
      isValidating.value = true
      
      const result = await validationEngine.validateForm(formData, options.ruleKey)
      
      // 更新状态
      validationErrors.value = result.errors
      validationWarnings.value = result.warnings

      // 更新字段状态
      for (const fieldName of Object.keys(formData)) {
        const fieldErrors = result.errors.filter(e => e.field_name === fieldName)
        const fieldWarnings = result.warnings.filter(w => w.includes(fieldName))
        
        fieldStates[fieldName] = {
          is_validating: false,
          has_errors: fieldErrors.length > 0,
          error_count: fieldErrors.length,
          warning_count: fieldWarnings.length,
          last_validation_time: new Date(),
          validation_duration: result.duration
        }
      }

      // 显示消息
      if (options.showMessages !== false) {
        if (result.valid) {
          ElMessage.success('表单校验通过')
        } else {
          ElMessage.error(`表单校验失败，发现${result.errors.length}个错误`)
        }
      }

      return result
    } catch (error) {
      console.error('表单校验失败:', error)
      if (options.showMessages !== false) {
        ElMessage.error('表单校验过程中发生错误')
      }
      throw error
    } finally {
      isValidating.value = false
    }
  }

  /**
   * 注册字段实时校验
   */
  const registerFieldValidation = (fieldName: string, element?: HTMLElement) => {
    realTimeValidator.registerField(fieldName, options.ruleKey, element)
    
    // 监听实时校验事件
    realTimeValidator.addEventListener('field_validated', (event: ValidationEvent) => {
      if (event.field_name === fieldName && event.result) {
        // 更新字段状态
        fieldStates[fieldName] = {
          is_validating: false,
          has_errors: !event.result.valid,
          error_count: event.result.errors.length,
          warning_count: event.result.warnings.length,
          last_validation_time: new Date(),
          validation_duration: event.result.duration
        }
        
        // 更新全局错误状态
        updateGlobalErrors()
      }
    })
  }

  /**
   * 更新全局错误状态
   */
  const updateGlobalErrors = () => {
    const allErrors: ValidationResult['errors'] = []
    const allWarnings: string[] = []
    
    for (const [fieldName, state] of Object.entries(fieldStates)) {
      if (state.has_errors) {
        // 这里简化处理，实际应该从校验结果中获取具体错误
        allErrors.push({
          field_name: fieldName,
          chinese_name: fieldName,
          error_code: 'FIELD_VALIDATION_ERROR',
          error_message: `${fieldName}校验失败`,
          error_value: null,
          rule_type: 'unknown',
          suggestions: [],
          severity: 'error' as any
        })
      }
    }
    
    validationErrors.value = allErrors
    validationWarnings.value = allWarnings
  }

  /**
   * 清除校验状态
   */
  const clearValidation = () => {
    validationErrors.value = []
    validationWarnings.value = []
    
    // 清除字段状态
    for (const fieldName of Object.keys(fieldStates)) {
      delete fieldStates[fieldName]
    }
    
    // 重置实时校验器
    realTimeValidator.resetAllFields()
  }

  /**
   * 清除单个字段校验
   */
  const clearFieldValidation = (fieldName: string) => {
    // 移除该字段的错误
    validationErrors.value = validationErrors.value.filter(e => e.field_name !== fieldName)
    validationWarnings.value = validationWarnings.value.filter(w => !w.includes(fieldName))
    
    // 清除字段状态
    delete fieldStates[fieldName]
    
    // 重置实时校验器中的字段
    realTimeValidator.resetField(fieldName)
  }

  /**
   * 获取字段错误信息
   */
  const getFieldErrors = (fieldName: string) => {
    return validationErrors.value.filter(e => e.field_name === fieldName)
  }

  /**
   * 获取字段中文名称
   */
  const getFieldChineseName = (fieldName: string): string => {
    const ruleConfig = validationRuleSync.getRuleConfig(options.ruleKey)
    return ruleConfig?.fields[fieldName]?.chinese_name || fieldName
  }

  /**
   * 获取字段校验规则
   */
  const getFieldRules = (fieldName: string): ElementFormRule[] => {
    return formRules.value[fieldName] || []
  }

  /**
   * 格式化错误消息
   */
  const formatErrorMessages = (errors: ValidationResult['errors'] = validationErrors.value): string => {
    return errors.map(error => error.error_message).join('; ')
  }

  /**
   * 获取错误汇总
   */
  const getErrorSummary = () => {
    const errorsByField: Record<string, ValidationResult['errors']> = {}
    
    for (const error of validationErrors.value) {
      if (!errorsByField[error.field_name]) {
        errorsByField[error.field_name] = []
      }
      errorsByField[error.field_name].push(error)
    }
    
    return errorsByField
  }

  // 生命周期
  onMounted(() => {
    initializeValidation()
  })

  onUnmounted(() => {
    realTimeValidator.destroy()
  })

  return {
    // 状态
    isValidating,
    validationErrors,
    validationWarnings,
    fieldStates,
    formRules,
    
    // 计算属性
    hasErrors,
    hasWarnings,
    isValid,
    errorCount,
    warningCount,
    
    // 方法
    validateField,
    validateForm,
    registerFieldValidation,
    clearValidation,
    clearFieldValidation,
    getFieldErrors,
    getFieldChineseName,
    getFieldRules,
    formatErrorMessages,
    getErrorSummary,
    
    // 工具方法
    initializeValidation
  }
}
