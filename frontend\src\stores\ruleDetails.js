import { ref, computed, nextTick, watch } from 'vue'
import { defineStore } from 'pinia'
import { ElMessage } from 'element-plus'
import { enhancedRuleDetailsApi } from '@/api/enhancedRuleDetails'
import { enhancedErrorHandler } from '@/utils/enhancedErrorHandler'
import { useAsyncState } from '@/composables/core/useAsyncState'
import { useStateMachine } from '@/composables/core/useStateMachine'
import { useFeedback } from '@/composables/ui/useFeedback'
import { useAppStore } from './app'

/**
 * 规则明细管理 Store
 * 专门处理规则明细的 CRUD 操作、批量处理、搜索统计等功能
 * 集成企业级状态管理架构，提供智能缓存和错误恢复
 */
export const useRuleDetailsStore = defineStore('ruleDetails', () => {
  // ==================== 依赖注入 ====================

  const appStore = useAppStore()
  const feedback = useFeedback()
  const stateMachine = useStateMachine()

  // ==================== 核心状态定义 ====================

  // 明细列表相关状态
  const detailsList = ref([])           // 当前明细列表
  const currentDetail = ref(null)       // 当前选中的明细
  const selectedDetails = ref([])       // 批量选中的明细

  // 分页和过滤状态
  const pagination = ref({
    page: 1,
    pageSize: 20,
    total: 0,
    hasNext: false,
    hasPrev: false
  })

  const filters = ref({
    status: null,
    search: '',
    errorLevel1: null,
    errorLevel2: null,
    ruleCategory: null,
    sortBy: 'created_at',
    sortOrder: 'desc'
  })

  // 增强操作状态
  const operationStatus = ref({
    lastOperation: null,
    operationTime: null,
    affectedCount: 0,
    errors: [],
    retryCount: 0,
    maxRetries: 3,
    isRecovering: false,
    lastSuccessTime: null,
    operationHistory: []
  })

  // 加载状态
  const loading = ref(false)
  const detailLoading = ref(false)
  const batchLoading = ref(false)
  const searchLoading = ref(false)

  // ==================== 增强API初始化 ====================

  // 初始化增强API
  const initializeApi = async () => {
    try {
      await enhancedRuleDetailsApi.initialize()
    } catch (error) {
      console.error('Failed to initialize enhanced API:', error)
      enhancedErrorHandler.handleError(error, {
        context: 'Store initialization',
        showMessage: true
      })
    }
  }

  // 自动初始化
  initializeApi()

  // ==================== 增强计算属性 ====================

  // 基础统计
  const detailsCount = computed(() => detailsList.value.length)
  const hasDetails = computed(() => detailsList.value.length > 0)
  const selectedCount = computed(() => selectedDetails.value.length)
  const hasSelected = computed(() => selectedDetails.value.length > 0)

  // 增强加载状态
  const isLoading = computed(() =>
    loading.value || detailLoading.value || batchLoading.value || searchLoading.value
  )

  const loadingState = computed(() => ({
    isLoading: isLoading.value,
    loadingType: loading.value ? 'list' :
      detailLoading.value ? 'detail' :
        batchLoading.value ? 'batch' :
          searchLoading.value ? 'search' : 'none',
    canOperate: !isLoading.value && !operationStatus.value.isRecovering
  }))

  // 增强分页信息
  const paginationInfo = computed(() => ({
    current: pagination.value.page,
    total: pagination.value.total,
    pageSize: pagination.value.pageSize,
    showTotal: `共 ${pagination.value.total} 条记录`,
    showSizeChanger: true,
    showQuickJumper: true,
    hasNext: pagination.value.hasNext,
    hasPrev: pagination.value.hasPrev,
    totalPages: Math.ceil(pagination.value.total / pagination.value.pageSize)
  }))

  // 智能过滤器摘要
  const activeFilters = computed(() => {
    const active = []
    if (filters.value.status) active.push(`状态: ${filters.value.status}`)
    if (filters.value.search) active.push(`搜索: ${filters.value.search}`)
    if (filters.value.errorLevel1) active.push(`错误类型: ${filters.value.errorLevel1}`)
    if (filters.value.ruleCategory) active.push(`规则类别: ${filters.value.ruleCategory}`)
    return active
  })

  const hasActiveFilters = computed(() => activeFilters.value.length > 0)

  // 操作状态摘要
  const operationSummary = computed(() => ({
    canRetry: operationStatus.value.retryCount < operationStatus.value.maxRetries,
    hasErrors: operationStatus.value.errors.length > 0,
    isHealthy: !operationStatus.value.isRecovering && operationStatus.value.errors.length === 0,
    lastOperationSuccess: operationStatus.value.lastSuccessTime !== null,
    operationRate: operationStatus.value.operationHistory.length > 0 ?
      operationStatus.value.operationHistory.filter(op => op.success).length /
      operationStatus.value.operationHistory.length : 1
  }))

  // 数据质量指标
  const dataQuality = computed(() => {
    if (!hasDetails.value) return { score: 100, issues: [] }

    const issues = []
    const duplicates = new Set()
    const seen = new Set()

    detailsList.value.forEach(detail => {
      const key = `${detail.rule_key}_${detail.id}`
      if (seen.has(key)) {
        duplicates.add(key)
      }
      seen.add(key)
    })

    if (duplicates.size > 0) {
      issues.push(`发现 ${duplicates.size} 条重复数据`)
    }

    const score = Math.max(0, 100 - (duplicates.size * 10))
    return { score, issues: issues }
  })

  // ==================== 状态监听和智能管理 ====================

  // 智能缓存管理
  const cacheManager = ref({
    lastClearTime: Date.now(),
    hitCount: 0,
    missCount: 0,
    autoCleanup: true,
    maxAge: 5 * 60 * 1000, // 5分钟
    maxSize: 100
  })

  // 监听操作状态变化
  watch(
    () => operationStatus.value.errors,
    (newErrors, oldErrors) => {
      if (newErrors.length > oldErrors.length) {
        // 新增错误，触发错误恢复
        const latestError = newErrors[newErrors.length - 1]
        handleOperationError(latestError)
      }
    },
    { deep: true }
  )

  // 监听过滤器变化，自动清理无效缓存
  watch(
    () => filters.value,
    () => {
      if (cacheManager.value.autoCleanup) {
        clearStaleCache()
      }
    },
    { deep: true }
  )

  // 操作错误处理
  const handleOperationError = async (error) => {
    operationStatus.value.isRecovering = true

    try {
      // 记录错误到历史
      operationStatus.value.operationHistory.push({
        timestamp: Date.now(),
        operation: operationStatus.value.lastOperation,
        success: false,
        error: error.message
      })

      // 智能错误恢复
      if (operationStatus.value.retryCount < operationStatus.value.maxRetries) {
        operationStatus.value.retryCount++
        feedback.toastInfo(`操作失败，正在重试 (${operationStatus.value.retryCount}/${operationStatus.value.maxRetries})`)

        // 延迟重试
        await new Promise(resolve => setTimeout(resolve, 1000 * operationStatus.value.retryCount))
      } else {
        feedback.toastError('操作失败，已达到最大重试次数')
        appStore.setError(error)
      }
    } finally {
      operationStatus.value.isRecovering = false
    }
  }

  // 清理过期缓存
  const clearStaleCache = async () => {
    try {
      const stats = await enhancedRuleDetailsApi.getCacheStats()
      if (stats && stats.size > cacheManager.value.maxSize) {
        await enhancedRuleDetailsApi.clearCache()
        cacheManager.value.lastClearTime = Date.now()
        cacheManager.value.hitCount = 0
        cacheManager.value.missCount = 0
      }
    } catch (error) {
      console.warn('清理缓存失败:', error)
    }
  }

  // ==================== 增强API调用封装 ====================

  /**
   * 企业级异步API调用封装
   * 集成状态机、重试机制、缓存管理和错误恢复
   * @param {Function} apiMethod - 增强API方法
   * @param {Ref} loadingRef - 加载状态引用
   * @param {string} operationName - 操作名称
   * @param {...any} args - API方法参数
   * @returns {Promise} API响应数据
   */
  const enhancedApiCall = async (apiMethod, loadingRef, operationName, ...args) => {
    const startTime = Date.now()

    // 更新操作状态
    operationStatus.value.lastOperation = operationName
    operationStatus.value.operationTime = startTime

    try {
      // 启动状态机
      await stateMachine.start()

      if (loadingRef) {
        loadingRef.value = true
        appStore.addLoadingTask(`ruleDetails_${operationName}`)
      }

      const response = await apiMethod(...args)

      if (response.success) {
        // 成功状态
        await stateMachine.success()

        // 更新缓存统计
        cacheManager.value.hitCount++

        // 记录成功操作
        operationStatus.value.lastSuccessTime = Date.now()
        operationStatus.value.retryCount = 0
        operationStatus.value.operationHistory.push({
          timestamp: startTime,
          operation: operationName,
          success: true,
          duration: Date.now() - startTime
        })

        feedback.toastSuccess(`${operationName}操作成功`)
        return response.data
      } else {
        // API返回错误
        const error = new Error(response.message || 'API调用失败')
        error.code = response.code
        error.response = response
        throw error
      }
    } catch (error) {
      // 错误状态
      await stateMachine.error()

      // 更新缓存统计
      cacheManager.value.missCount++

      // 记录错误
      operationStatus.value.errors.push({
        timestamp: Date.now(),
        operation: operationName,
        message: error.message,
        code: error.code
      })

      // 使用增强错误处理
      enhancedErrorHandler.handleError(error, {
        context: `Store ${operationName} 操作`,
        showMessage: true,
        enableRetry: operationStatus.value.retryCount < operationStatus.value.maxRetries
      })

      throw error
    } finally {
      if (loadingRef) {
        loadingRef.value = false
        appStore.removeLoadingTask(`ruleDetails_${operationName}`)
      }
    }
  }

  // ==================== 核心 CRUD 操作 ====================

  /**
   * 获取规则明细列表
   * @param {string} ruleKey - 规则键
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 明细列表数据
   */
  const fetchDetailsList = async (ruleKey, params = {}) => {
    if (!ruleKey) return null

    const queryParams = {
      page: pagination.value.page,
      page_size: pagination.value.pageSize,
      ...filters.value,
      ...params
    }

    // 使用增强API调用
    const result = await enhancedApiCall(
      enhancedRuleDetailsApi.getRuleDetailsList,
      loading,
      '获取规则明细列表',
      ruleKey,
      queryParams
    )

    if (result) {
      // 更新状态 - 增强API已经处理了字段映射
      detailsList.value = result.items || []
      pagination.value = {
        page: result.page || 1,
        pageSize: result.page_size || 20,
        total: result.total || 0,
        hasNext: result.has_next || false,
        hasPrev: result.has_prev || false
      }
    }

    return result
  }

  /**
   * 获取单条规则明细
   * @param {string} ruleKey - 规则键
   * @param {number|string} detailId - 明细ID
   * @returns {Promise<Object>} 明细数据
   */
  const fetchDetailById = async (ruleKey, detailId) => {
    if (!ruleKey || !detailId) return null

    // 使用增强API调用
    const result = await enhancedApiCall(
      enhancedRuleDetailsApi.getRuleDetailById,
      detailLoading,
      '获取规则明细详情',
      ruleKey,
      detailId
    )

    if (result) {
      // 增强API已经处理了字段映射
      currentDetail.value = result
    }

    return result
  }

  /**
   * 创建规则明细
   * @param {string} ruleKey - 规则键
   * @param {Object} detailData - 明细数据
   * @returns {Promise<Object>} 创建结果
   */
  const createDetail = async (ruleKey, detailData) => {
    if (!ruleKey || !detailData) return null

    // 使用增强API调用
    const result = await enhancedApiCall(
      enhancedRuleDetailsApi.createRuleDetail,
      loading,
      '创建规则明细',
      ruleKey,
      detailData
    )

    if (result) {
      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'create',
        operationTime: Date.now(),
        affectedCount: 1,
        errors: []
      }

      ElMessage.success('创建明细成功')
    }

    return result
  }

  /**
   * 更新规则明细
   * @param {string} ruleKey - 规则键
   * @param {number|string} detailId - 明细ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  const updateDetail = async (ruleKey, detailId, updateData) => {
    if (!ruleKey || !detailId || !updateData) return null

    // 使用增强API调用
    const result = await enhancedApiCall(
      enhancedRuleDetailsApi.updateRuleDetail,
      loading,
      ruleKey,
      detailId,
      updateData
    )

    if (result) {
      // 更新本地状态
      const index = detailsList.value.findIndex(d => d.id === detailId)
      if (index !== -1) {
        detailsList.value[index] = { ...detailsList.value[index], ...result }
      }

      if (currentDetail.value && currentDetail.value.id === detailId) {
        currentDetail.value = { ...currentDetail.value, ...result }
      }

      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'update',
        operationTime: Date.now(),
        affectedCount: 1,
        errors: []
      }

      ElMessage.success('更新明细成功')
    }

    return result
  }

  /**
   * 删除规则明细
   * @param {string} ruleKey - 规则键
   * @param {number|string} detailId - 明细ID
   * @returns {Promise<boolean>} 删除结果
   */
  const deleteDetail = async (ruleKey, detailId) => {
    if (!ruleKey || !detailId) return false

    // 使用增强API调用
    const result = await enhancedApiCall(
      enhancedRuleDetailsApi.deleteRuleDetail,
      loading,
      ruleKey,
      detailId
    )

    if (result) {
      // 更新本地状态
      detailsList.value = detailsList.value.filter(d => d.id !== detailId)

      if (currentDetail.value && currentDetail.value.id === detailId) {
        currentDetail.value = null
      }

      // 从选中列表中移除
      selectedDetails.value = selectedDetails.value.filter(d => d.id !== detailId)

      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'delete',
        operationTime: Date.now(),
        affectedCount: 1,
        errors: []
      }

      ElMessage.success('删除明细成功')
    }

    return result
  }

  // ==================== 批量操作方法 ====================

  /**
   * 批量创建规则明细
   * @param {string} ruleKey - 规则键
   * @param {Array} detailsData - 明细数据数组
   * @returns {Promise<Object>} 批量创建结果
   */
  const batchCreateDetails = async (ruleKey, detailsData) => {
    if (!ruleKey || !Array.isArray(detailsData) || detailsData.length === 0) {
      return null
    }

    // 使用增强API调用
    const result = await enhancedApiCall(
      enhancedRuleDetailsApi.batchCreateRuleDetails,
      batchLoading,
      ruleKey,
      detailsData
    )

    if (result) {
      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'batchCreate',
        operationTime: Date.now(),
        affectedCount: result.successCount || 0,
        errors: result.errors || []
      }

      const message = result.errors && result.errors.length > 0
        ? `批量创建完成，成功 ${result.successCount} 条，失败 ${result.errors.length} 条`
        : `批量创建成功，共 ${result.successCount} 条`

      ElMessage.success(message)
    }

    return result
  }

  /**
   * 批量更新规则明细
   * @param {string} ruleKey - 规则键
   * @param {Array} updates - 更新数据数组
   * @returns {Promise<Object>} 批量更新结果
   */
  const batchUpdateDetails = async (ruleKey, updates) => {
    if (!ruleKey || !Array.isArray(updates) || updates.length === 0) {
      return null
    }

    // 乐观更新：先更新本地状态
    const originalDetails = [...detailsList.value]

    try {
      // 应用乐观更新
      updates.forEach(update => {
        const index = detailsList.value.findIndex(d => d.id === update.id)
        if (index !== -1) {
          detailsList.value[index] = { ...detailsList.value[index], ...update.data }
        }
      })

      // 使用增强API调用
      const result = await enhancedApiCall(
        enhancedRuleDetailsApi.batchUpdateRuleDetails,
        batchLoading,
        ruleKey,
        updates
      )

      if (result) {
        // 同步服务器结果
        if (result.updatedDetails) {
          result.updatedDetails.forEach(detail => {
            const index = detailsList.value.findIndex(d => d.id === detail.id)
            if (index !== -1) {
              detailsList.value[index] = detail
            }
          })
        }

        // 更新操作状态
        operationStatus.value = {
          lastOperation: 'batchUpdate',
          operationTime: Date.now(),
          affectedCount: result.successCount || 0,
          errors: result.errors || []
        }

        const message = result.errors && result.errors.length > 0
          ? `批量更新完成，成功 ${result.successCount} 条，失败 ${result.errors.length} 条`
          : `批量更新成功，共 ${result.successCount} 条`

        ElMessage.success(message)
      }

      return result
    } catch (error) {
      // 回滚乐观更新
      detailsList.value = originalDetails
      throw error
    }
  }

  /**
   * 批量删除规则明细
   * @param {string} ruleKey - 规则键
   * @param {Array} detailIds - 明细ID数组
   * @returns {Promise<Object>} 批量删除结果
   */
  const batchDeleteDetails = async (ruleKey, detailIds) => {
    if (!ruleKey || !Array.isArray(detailIds) || detailIds.length === 0) {
      return null
    }

    // 使用增强API调用
    const result = await enhancedApiCall(
      enhancedRuleDetailsApi.batchDeleteRuleDetails,
      batchLoading,
      ruleKey,
      detailIds
    )

    if (result) {
      // 更新本地状态
      detailsList.value = detailsList.value.filter(d => !detailIds.includes(d.id))
      selectedDetails.value = selectedDetails.value.filter(d => !detailIds.includes(d.id))

      if (currentDetail.value && detailIds.includes(currentDetail.value.id)) {
        currentDetail.value = null
      }

      // 更新操作状态
      operationStatus.value = {
        lastOperation: 'batchDelete',
        operationTime: Date.now(),
        affectedCount: result.successCount || 0,
        errors: result.errors || []
      }

      const message = result.errors && result.errors.length > 0
        ? `批量删除完成，成功 ${result.successCount} 条，失败 ${result.errors.length} 条`
        : `批量删除成功，共 ${result.successCount} 条`

      ElMessage.success(message)
    }

    return result
  }

  // ==================== 搜索和过滤方法 ====================

  /**
   * 搜索规则明细
   * @param {string} ruleKey - 规则键
   * @param {Object} searchParams - 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  const searchDetails = async (ruleKey, searchParams = {}) => {
    if (!ruleKey) return null

    const queryParams = {
      page: pagination.value.page,
      page_size: pagination.value.pageSize,
      ...searchParams
    }

    // 使用增强API调用
    const result = await enhancedApiCall(
      enhancedRuleDetailsApi.searchRuleDetails,
      searchLoading,
      ruleKey,
      queryParams
    )

    if (result) {
      // 更新状态 - 增强API已经处理了字段映射
      detailsList.value = result.items || []
      pagination.value = {
        page: result.page || 1,
        pageSize: result.page_size || 20,
        total: result.total || 0,
        hasNext: result.has_next || false,
        hasPrev: result.has_prev || false
      }
    }

    return result
  }

  /**
   * 更新过滤条件
   * @param {Object} newFilters - 新的过滤条件
   */
  const updateFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  /**
   * 重置过滤条件
   */
  const resetFilters = () => {
    filters.value = {
      status: null,
      search: '',
      errorLevel1: null,
      errorLevel2: null,
      ruleCategory: null,
      sortBy: 'created_at',
      sortOrder: 'desc'
    }
  }

  /**
   * 更新分页信息
   * @param {Object} newPagination - 新的分页信息
   */
  const updatePagination = (newPagination) => {
    pagination.value = { ...pagination.value, ...newPagination }
  }

  // ==================== 选择管理方法 ====================

  /**
   * 选择单条明细
   * @param {Object} detail - 明细对象
   */
  const selectDetail = (detail) => {
    if (!selectedDetails.value.find(d => d.id === detail.id)) {
      selectedDetails.value.push(detail)
    }
  }

  /**
   * 取消选择单条明细
   * @param {Object} detail - 明细对象
   */
  const unselectDetail = (detail) => {
    selectedDetails.value = selectedDetails.value.filter(d => d.id !== detail.id)
  }

  /**
   * 切换明细选择状态
   * @param {Object} detail - 明细对象
   */
  const toggleDetailSelection = (detail) => {
    const isSelected = selectedDetails.value.find(d => d.id === detail.id)
    if (isSelected) {
      unselectDetail(detail)
    } else {
      selectDetail(detail)
    }
  }

  /**
   * 全选/取消全选
   * @param {boolean} selectAll - 是否全选
   */
  const toggleSelectAll = (selectAll) => {
    if (selectAll) {
      selectedDetails.value = [...detailsList.value]
    } else {
      selectedDetails.value = []
    }
  }

  /**
   * 清除所有选择
   */
  const clearSelection = () => {
    selectedDetails.value = []
  }

  // ==================== 增强API缓存管理 ====================

  /**
   * 清除增强API缓存
   * @param {string} ruleKey - 规则键（可选）
   */
  const clearEnhancedCache = async (ruleKey = null) => {
    try {
      if (ruleKey) {
        await enhancedRuleDetailsApi.clearCache(ruleKey)
      } else {
        await enhancedRuleDetailsApi.clearAllCache()
      }
    } catch (error) {
      console.warn('清除增强API缓存失败:', error)
    }
  }

  /**
   * 获取增强API缓存统计
   * @returns {Promise<Object>} 缓存统计
   */
  const getEnhancedCacheStats = async () => {
    try {
      return await enhancedRuleDetailsApi.getCacheStats()
    } catch (error) {
      console.warn('获取缓存统计失败:', error)
      return { totalSize: 0, hitRate: 0 }
    }
  }

  // ==================== 状态管理方法 ====================

  /**
   * 设置当前明细
   * @param {Object} detail - 明细对象
   */
  const setCurrentDetail = (detail) => {
    currentDetail.value = detail
  }

  /**
   * 清除当前明细
   */
  const clearCurrentDetail = () => {
    currentDetail.value = null
  }

  /**
   * 重置整个 Store 状态
   */
  const resetStore = async () => {
    detailsList.value = []
    currentDetail.value = null
    selectedDetails.value = []

    pagination.value = {
      page: 1,
      pageSize: 20,
      total: 0,
      hasNext: false,
      hasPrev: false
    }

    resetFilters()

    operationStatus.value = {
      lastOperation: null,
      operationTime: null,
      affectedCount: 0,
      errors: []
    }

    loading.value = false
    detailLoading.value = false
    batchLoading.value = false
    searchLoading.value = false

    // 清除增强API缓存
    await clearEnhancedCache()
  }

  // ==================== 返回公共接口 ====================

  return {
    // 核心状态
    detailsList,
    currentDetail,
    selectedDetails,
    pagination,
    filters,
    operationStatus,

    // 加载状态
    loading,
    detailLoading,
    batchLoading,
    searchLoading,

    // 增强计算属性
    detailsCount,
    hasDetails,
    selectedCount,
    hasSelected,
    isLoading,
    loadingState,
    paginationInfo,
    activeFilters,
    hasActiveFilters,
    operationSummary,
    dataQuality,

    // 核心 CRUD 操作
    fetchDetailsList,
    fetchDetailById,
    createDetail,
    updateDetail,
    deleteDetail,

    // 批量操作
    batchCreateDetails,
    batchUpdateDetails,
    batchDeleteDetails,

    // 搜索和过滤
    searchDetails,
    updateFilters,
    resetFilters,
    updatePagination,

    // 选择管理
    selectDetail,
    unselectDetail,
    toggleDetailSelection,
    toggleSelectAll,
    clearSelection,

    // 增强API缓存管理
    clearEnhancedCache,
    getEnhancedCacheStats,
    clearStaleCache,

    // 增强状态管理
    setCurrentDetail,
    clearCurrentDetail,
    resetStore,
    handleOperationError,

    // 缓存管理器
    cacheManager
  }
})
