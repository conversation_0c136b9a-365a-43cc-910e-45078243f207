"""
Excel模板生成服务 - 元数据驱动版本
基于RuleFieldMetadata表的元数据动态生成Excel模板
支持中文界面、校验规则集成和动态列生成
"""

import time
from dataclasses import dataclass
from pathlib import Path
from typing import Any

import openpyxl
from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
from openpyxl.worksheet.datavalidation import DataValidation
from sqlalchemy.orm import Session

from core.logging.logging_system import log as logger
from models.database import RuleTemplate
from services.rule_detail_service import ServiceError
from tools.field_mapping_manager import FieldMappingManager


@dataclass
class ColumnDefinition:
    """列定义"""

    field_name: str
    chinese_name: str
    data_type: str
    is_required: bool
    max_length: int | None = None
    validation_rules: list[str] = None
    default_value: Any = None
    description: str = ""
    excel_order: int = 0


@dataclass
class TemplateMetadata:
    """模板元数据"""

    rule_key: str
    rule_name: str
    template_name: str
    columns: list[ColumnDefinition]
    total_columns: int
    required_columns: int
    optional_columns: int
    description: str = ""


class ExcelTemplateService:
    """Excel模板生成服务 - 元数据驱动"""

    def __init__(self, session: Session, output_dir: str = "generated_templates"):
        """
        初始化服务

        Args:
            session: 数据库会话
            output_dir: 模板输出目录
        """
        self.session = session
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.field_mapping_manager = FieldMappingManager()

        # 样式定义
        self._init_styles()

    def _init_styles(self):
        """初始化Excel样式"""
        # 标题样式
        self.header_font = Font(name="微软雅黑", size=12, bold=True, color="FFFFFF")
        self.header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        self.header_alignment = Alignment(horizontal="center", vertical="center")

        # 必填字段样式
        self.required_fill = PatternFill(start_color="FFE6E6", end_color="FFE6E6", fill_type="solid")

        # 边框样式
        thin_border = Side(border_style="thin", color="000000")
        self.border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)

        # 数据样式
        self.data_font = Font(name="微软雅黑", size=10)
        self.data_alignment = Alignment(horizontal="left", vertical="center")

    def generate_template_by_rule_key(self, rule_key: str) -> Path:
        """
        根据规则键生成Excel模板

        Args:
            rule_key: 规则模板键

        Returns:
            Path: 生成的模板文件路径

        Raises:
            ServiceException: 当生成失败时
        """
        start_time = time.time()

        try:
            # 1. 获取模板元数据
            template_metadata = self.get_template_metadata(rule_key)

            # 2. 创建工作簿
            workbook = openpyxl.Workbook()
            sheet = workbook.active
            sheet.title = template_metadata.template_name

            # 3. 生成表头
            self._generate_headers(sheet, template_metadata.columns)

            # 4. 添加数据验证
            self._add_data_validation(sheet, template_metadata.columns)

            # 5. 设置列宽
            self._set_column_widths(sheet, template_metadata.columns)

            # 6. 添加说明工作表
            self._add_instruction_sheet(workbook, template_metadata)

            # 7. 保存文件
            output_path = self.output_dir / f"{template_metadata.template_name}-规则模板.xlsx"
            workbook.save(output_path)

            # 8. 记录日志
            duration = time.time() - start_time
            logger.info(
                f"成功生成Excel模板: rule_key={rule_key}, "
                f"columns={template_metadata.total_columns}, "
                f"duration={duration:.2f}s, "
                f"path={output_path}"
            )

            return output_path

        except Exception as e:
            error_msg = f"生成Excel模板失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="TEMPLATE_GENERATION_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def get_template_columns(self, rule_key: str) -> list[ColumnDefinition]:
        """
        获取模板列定义

        Args:
            rule_key: 规则模板键

        Returns:
            List[ColumnDefinition]: 列定义列表
        """
        try:
            # 获取规则模板
            template = self._get_rule_template(rule_key)
            if not template:
                raise ServiceError(
                    f"规则模板 '{rule_key}' 不存在", error_code="TEMPLATE_NOT_FOUND", details={"rule_key": rule_key}
                )

            # 获取字段元数据
            field_metadata_list = template.get_field_metadata_list()

            # 转换为列定义
            columns = []
            for metadata in field_metadata_list:
                # 处理field_type可能是枚举对象的情况
                field_type = metadata.field_type
                if hasattr(field_type, "value"):
                    field_type = field_type.value

                column = ColumnDefinition(
                    field_name=metadata.field_name,
                    chinese_name=metadata.display_name,
                    data_type=field_type,
                    is_required=metadata.is_required,
                    validation_rules=metadata.get_validation_rules(),
                    default_value=metadata.get_default_value_parsed(),
                    description=metadata.description or "",
                    excel_order=metadata.excel_column_order or 999,
                )
                columns.append(column)

            # 按Excel列顺序排序
            columns.sort(key=lambda x: x.excel_order)

            logger.debug(f"获取模板列定义: rule_key={rule_key}, columns={len(columns)}")
            return columns

        except ServiceError:
            raise
        except Exception as e:
            error_msg = f"获取模板列定义失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="GET_COLUMNS_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def get_template_metadata(self, rule_key: str) -> TemplateMetadata:
        """
        获取模板元数据

        Args:
            rule_key: 规则模板键

        Returns:
            TemplateMetadata: 模板元数据
        """
        try:
            # 获取规则模板
            template = self._get_rule_template(rule_key)
            if not template:
                raise ServiceError(
                    f"规则模板 '{rule_key}' 不存在", error_code="TEMPLATE_NOT_FOUND", details={"rule_key": rule_key}
                )

            # 获取列定义
            columns = self.get_template_columns(rule_key)

            # 统计信息
            required_columns = sum(1 for col in columns if col.is_required)
            optional_columns = len(columns) - required_columns

            # 构建元数据
            metadata = TemplateMetadata(
                rule_key=rule_key,
                rule_name=template.name,
                template_name=template.name,
                columns=columns,
                total_columns=len(columns),
                required_columns=required_columns,
                optional_columns=optional_columns,
                description=template.description or "",
            )

            logger.debug(f"获取模板元数据: rule_key={rule_key}, total_columns={len(columns)}")
            return metadata

        except ServiceError:
            raise
        except Exception as e:
            error_msg = f"获取模板元数据失败: rule_key={rule_key}, error={str(e)}"
            logger.error(error_msg)
            raise ServiceError(
                error_msg, error_code="GET_METADATA_FAILED", details={"rule_key": rule_key, "error": str(e)}
            ) from None

    def _get_rule_template(self, rule_key: str) -> RuleTemplate | None:
        """获取规则模板"""
        return self.session.query(RuleTemplate).filter(RuleTemplate.rule_key == rule_key).first()

    def _generate_headers(self, sheet, columns: list[ColumnDefinition]):
        """生成表头"""
        # 设置表头
        for col_idx, column in enumerate(columns, 1):
            cell = sheet.cell(row=1, column=col_idx)

            # 设置表头文本
            header_text = column.chinese_name
            if column.is_required:
                header_text += " *"
            cell.value = header_text

            # 设置样式
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.header_alignment
            cell.border = self.border

            # 必填字段特殊标记
            if column.is_required:
                cell.fill = PatternFill(start_color="FF6B6B", end_color="FF6B6B", fill_type="solid")

    def _add_data_validation(self, sheet, columns: list[ColumnDefinition]):
        """添加数据验证"""
        for col_idx, column in enumerate(columns, 1):
            col_letter = openpyxl.utils.get_column_letter(col_idx)

            # 根据数据类型添加验证
            if column.data_type == "integer":
                # 整数验证
                dv = DataValidation(type="whole", operator="greaterThanOrEqual", formula1=0)
                dv.error = "请输入有效的整数"
                dv.errorTitle = "数据验证错误"
                sheet.add_data_validation(dv)
                dv.add(f"{col_letter}2:{col_letter}1000")

            elif column.data_type == "string" and column.max_length:
                # 字符串长度验证
                dv = DataValidation(type="textLength", operator="lessThanOrEqual", formula1=column.max_length)
                dv.error = f"文本长度不能超过{column.max_length}个字符"
                dv.errorTitle = "数据验证错误"
                sheet.add_data_validation(dv)
                dv.add(f"{col_letter}2:{col_letter}1000")

    def _set_column_widths(self, sheet, columns: list[ColumnDefinition]):
        """设置列宽"""
        for col_idx, column in enumerate(columns, 1):
            col_letter = openpyxl.utils.get_column_letter(col_idx)

            # 根据字段类型和内容设置列宽
            if column.data_type == "text":
                width = 30  # 长文本字段
            elif column.data_type == "integer":
                width = 12  # 数字字段
            elif len(column.chinese_name) > 10:
                width = 20  # 长标题
            else:
                width = 15  # 默认宽度

            sheet.column_dimensions[col_letter].width = width

    def _add_instruction_sheet(self, workbook, metadata: TemplateMetadata):
        """添加说明工作表"""
        # 创建说明工作表
        instruction_sheet = workbook.create_sheet("填写说明")

        # 添加说明内容
        instructions = [
            f"规则模板：{metadata.rule_name}",
            f"模板说明：{metadata.description}",
            "",
            "填写要求：",
            f"• 必填字段：{metadata.required_columns}个（标记为红色）",
            f"• 可选字段：{metadata.optional_columns}个",
            f"• 总字段数：{metadata.total_columns}个",
            "",
            "字段说明：",
        ]

        # 添加字段详细说明
        for column in metadata.columns:
            field_desc = f"• {column.chinese_name}"
            if column.is_required:
                field_desc += "（必填）"
            if column.description:
                field_desc += f"：{column.description}"
            instructions.append(field_desc)

        # 写入说明内容
        for row_idx, instruction in enumerate(instructions, 1):
            cell = instruction_sheet.cell(row=row_idx, column=1)
            cell.value = instruction
            cell.font = Font(name="微软雅黑", size=10)

        # 设置列宽
        instruction_sheet.column_dimensions["A"].width = 50

    def validate_template_data(self, rule_key: str, data: list[dict[str, Any]]) -> dict[str, Any]:
        """
        验证模板数据

        Args:
            rule_key: 规则模板键
            data: 待验证的数据列表

        Returns:
            Dict: 验证结果
        """
        try:
            columns = self.get_template_columns(rule_key)
            errors = []
            warnings = []

            for row_idx, row_data in enumerate(data, 1):
                row_errors = []

                # 验证每个字段
                for column in columns:
                    field_value = row_data.get(column.chinese_name)

                    # 必填字段验证
                    if column.is_required and (field_value is None or field_value == ""):
                        row_errors.append(f"必填字段 '{column.chinese_name}' 不能为空")
                        continue

                    # 数据类型验证
                    if field_value is not None and field_value != "":
                        if column.data_type == "integer":
                            try:
                                int(field_value)
                            except (ValueError, TypeError):
                                row_errors.append(f"字段 '{column.chinese_name}' 必须为整数")

                        elif column.data_type == "string" and column.max_length:
                            if len(str(field_value)) > column.max_length:
                                row_errors.append(f"字段 '{column.chinese_name}' 长度不能超过{column.max_length}个字符")

                if row_errors:
                    errors.append(f"第{row_idx}行: {'; '.join(row_errors)}")

            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "total_rows": len(data),
                "error_rows": len(errors),
            }

        except Exception as e:
            logger.error(f"验证模板数据失败: rule_key={rule_key}, error={e}")
            return {
                "valid": False,
                "errors": [f"验证过程中发生错误: {str(e)}"],
                "warnings": [],
                "total_rows": len(data) if data else 0,
                "error_rows": 1,
            }
