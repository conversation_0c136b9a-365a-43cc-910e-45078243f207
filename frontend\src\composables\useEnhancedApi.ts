/**
 * 增强API组合式函数
 * 提供统一的API调用接口，集成字段映射、缓存和错误处理
 */

import { ref, computed, onMounted } from 'vue'
import { enhancedRuleDetailsApi } from '../api/enhancedRuleDetails'
import type {
  PaginationParams,
  SearchParams,
  BatchOperationRequest,
  StatisticsData
} from '../types/apiEnhanced'
import type {
  RuleDetail,
  CreateRuleDetailData,
  UpdateRuleDetailData
} from '../types/ruleDetails'

/**
 * 增强API状态管理
 */
export function useEnhancedApi() {
  // 初始化状态
  const initialized = ref(false)
  const initializing = ref(false)

  /**
   * 初始化API
   */
  const initialize = async () => {
    if (initialized.value || initializing.value) return

    initializing.value = true
    try {
      await enhancedRuleDetailsApi.initialize()
      initialized.value = true
    } catch (error) {
      console.error('Failed to initialize enhanced API:', error)
      throw error
    } finally {
      initializing.value = false
    }
  }

  // 自动初始化
  onMounted(() => {
    initialize()
  })

  return {
    initialized: computed(() => initialized.value),
    initializing: computed(() => initializing.value),
    initialize
  }
}

/**
 * 规则明细管理组合式函数
 */
export function useRuleDetails(ruleKey: string) {
  const { initialized, initialize } = useEnhancedApi()

  // 状态管理
  const loading = ref(false)
  const error = ref<string | null>(null)
  const ruleDetails = ref<RuleDetail[]>([])
  const currentDetail = ref<RuleDetail | null>(null)
  const pagination = ref({
    page: 1,
    page_size: 20,
    total: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  })

  // 搜索和过滤状态
  const searchParams = ref<SearchParams>({})
  const paginationParams = ref<PaginationParams>({
    page: 1,
    page_size: 20,
    sort_by: 'created_at',
    sort_order: 'desc'
  })

  /**
   * 获取规则明细列表
   */
  const fetchRuleDetailsList = async (params?: Partial<PaginationParams & SearchParams>) => {
    if (!initialized.value) {
      await initialize()
    }

    loading.value = true
    error.value = null

    try {
      const mergedParams = {
        ...paginationParams.value,
        ...searchParams.value,
        ...params
      }

      const response = await enhancedRuleDetailsApi.getRuleDetailsList(ruleKey, mergedParams)
      
      if (response.success && response.data) {
        ruleDetails.value = response.data.items
        pagination.value = {
          page: response.data.page,
          page_size: response.data.page_size,
          total: response.data.total,
          total_pages: response.data.total_pages,
          has_next: response.data.has_next,
          has_prev: response.data.has_prev
        }
      }

      return response
    } catch (err: any) {
      error.value = err.userMessage || err.message || '获取规则明细列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取单条规则明细
   */
  const fetchRuleDetailById = async (detailId: string | number) => {
    if (!initialized.value) {
      await initialize()
    }

    loading.value = true
    error.value = null

    try {
      const response = await enhancedRuleDetailsApi.getRuleDetailById(ruleKey, detailId)
      
      if (response.success && response.data) {
        currentDetail.value = response.data
      }

      return response
    } catch (err: any) {
      error.value = err.userMessage || err.message || '获取规则明细详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 创建规则明细
   */
  const createRuleDetail = async (data: CreateRuleDetailData) => {
    if (!initialized.value) {
      await initialize()
    }

    loading.value = true
    error.value = null

    try {
      const response = await enhancedRuleDetailsApi.createRuleDetail(ruleKey, data)
      
      if (response.success) {
        // 刷新列表
        await fetchRuleDetailsList()
      }

      return response
    } catch (err: any) {
      error.value = err.userMessage || err.message || '创建规则明细失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 更新规则明细
   */
  const updateRuleDetail = async (detailId: string | number, data: UpdateRuleDetailData) => {
    if (!initialized.value) {
      await initialize()
    }

    loading.value = true
    error.value = null

    try {
      const response = await enhancedRuleDetailsApi.updateRuleDetail(ruleKey, detailId, data)
      
      if (response.success) {
        // 更新当前详情
        if (currentDetail.value && currentDetail.value.id === detailId) {
          currentDetail.value = response.data!
        }
        
        // 刷新列表
        await fetchRuleDetailsList()
      }

      return response
    } catch (err: any) {
      error.value = err.userMessage || err.message || '更新规则明细失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 删除规则明细
   */
  const deleteRuleDetail = async (detailId: string | number) => {
    if (!initialized.value) {
      await initialize()
    }

    loading.value = true
    error.value = null

    try {
      const response = await enhancedRuleDetailsApi.deleteRuleDetail(ruleKey, detailId)
      
      if (response.success) {
        // 清除当前详情
        if (currentDetail.value && currentDetail.value.id === detailId) {
          currentDetail.value = null
        }
        
        // 刷新列表
        await fetchRuleDetailsList()
      }

      return response
    } catch (err: any) {
      error.value = err.userMessage || err.message || '删除规则明细失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 批量操作规则明细
   */
  const batchOperateRuleDetails = async (
    request: BatchOperationRequest<CreateRuleDetailData | UpdateRuleDetailData>
  ) => {
    if (!initialized.value) {
      await initialize()
    }

    loading.value = true
    error.value = null

    try {
      const response = await enhancedRuleDetailsApi.batchOperateRuleDetails(ruleKey, request)
      
      if (response.success) {
        // 刷新列表
        await fetchRuleDetailsList()
      }

      return response
    } catch (err: any) {
      error.value = err.userMessage || err.message || '批量操作失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * 搜索规则明细
   */
  const searchRuleDetails = async (params: SearchParams) => {
    searchParams.value = params
    paginationParams.value.page = 1 // 重置到第一页
    return fetchRuleDetailsList()
  }

  /**
   * 分页操作
   */
  const changePage = async (page: number) => {
    paginationParams.value.page = page
    return fetchRuleDetailsList()
  }

  const changePageSize = async (pageSize: number) => {
    paginationParams.value.page_size = pageSize
    paginationParams.value.page = 1 // 重置到第一页
    return fetchRuleDetailsList()
  }

  /**
   * 排序操作
   */
  const changeSort = async (sortBy: string, sortOrder: 'asc' | 'desc' = 'desc') => {
    paginationParams.value.sort_by = sortBy
    paginationParams.value.sort_order = sortOrder
    paginationParams.value.page = 1 // 重置到第一页
    return fetchRuleDetailsList()
  }

  /**
   * 刷新数据
   */
  const refresh = async () => {
    return fetchRuleDetailsList()
  }

  /**
   * 清除错误
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * 重置状态
   */
  const reset = () => {
    ruleDetails.value = []
    currentDetail.value = null
    error.value = null
    searchParams.value = {}
    paginationParams.value = {
      page: 1,
      page_size: 20,
      sort_by: 'created_at',
      sort_order: 'desc'
    }
  }

  return {
    // 状态
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    ruleDetails: computed(() => ruleDetails.value),
    currentDetail: computed(() => currentDetail.value),
    pagination: computed(() => pagination.value),
    searchParams: computed(() => searchParams.value),
    paginationParams: computed(() => paginationParams.value),

    // 方法
    fetchRuleDetailsList,
    fetchRuleDetailById,
    createRuleDetail,
    updateRuleDetail,
    deleteRuleDetail,
    batchOperateRuleDetails,
    searchRuleDetails,
    changePage,
    changePageSize,
    changeSort,
    refresh,
    clearError,
    reset
  }
}

/**
 * 规则明细统计组合式函数
 */
export function useRuleDetailsStatistics(ruleKey: string) {
  const { initialized, initialize } = useEnhancedApi()

  const loading = ref(false)
  const error = ref<string | null>(null)
  const statistics = ref<StatisticsData | null>(null)

  /**
   * 获取统计数据
   */
  const fetchStatistics = async () => {
    if (!initialized.value) {
      await initialize()
    }

    loading.value = true
    error.value = null

    try {
      const response = await enhancedRuleDetailsApi.getRuleDetailsStatistics(ruleKey)
      
      if (response.success && response.data) {
        statistics.value = response.data
      }

      return response
    } catch (err: any) {
      error.value = err.userMessage || err.message || '获取统计数据失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    statistics: computed(() => statistics.value),
    fetchStatistics
  }
}

/**
 * API缓存管理组合式函数
 */
export function useApiCache() {
  /**
   * 获取缓存统计
   */
  const getCacheStats = () => {
    return enhancedRuleDetailsApi.getCacheStats()
  }

  /**
   * 清除所有缓存
   */
  const clearAllCache = () => {
    enhancedRuleDetailsApi.clearAllCache()
  }

  return {
    getCacheStats,
    clearAllCache
  }
}
