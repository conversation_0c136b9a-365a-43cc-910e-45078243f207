/**
 * 测试环境设置
 * 配置全局测试环境、Mock和工具函数
 */

import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// 全局测试配置
beforeAll(() => {
  console.log('🔧 初始化测试环境...')
  
  // 设置全局性能监控
  if (!global.performance) {
    global.performance = {
      now: () => Date.now(),
      memory: {
        usedJSHeapSize: 1024 * 1024 * 20, // 20MB
        totalJSHeapSize: 1024 * 1024 * 50, // 50MB
        jsHeapSizeLimit: 1024 * 1024 * 100 // 100MB
      }
    }
  }
  
  // 设置全局垃圾回收（如果支持）
  if (typeof global.gc === 'undefined') {
    global.gc = vi.fn()
  }
  
  // 设置测试超时
  vi.setConfig({ testTimeout: 30000 })
})

afterAll(() => {
  console.log('🧹 清理测试环境...')
  vi.clearAllMocks()
  vi.clearAllTimers()
})

// Vue Test Utils 全局配置
config.global.mocks = {
  $t: (key) => key, // 国际化Mock
  $route: {
    params: {},
    query: {},
    path: '/'
  },
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn()
  }
}

// 全局组件Stub
config.global.stubs = {
  'router-link': true,
  'router-view': true,
  'el-icon': true,
  'transition': false,
  'transition-group': false
}

// Mock Element Plus
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    },
    ElMessageBox: {
      confirm: vi.fn().mockResolvedValue('confirm'),
      alert: vi.fn().mockResolvedValue('confirm'),
      prompt: vi.fn().mockResolvedValue({ value: 'test' })
    },
    ElNotification: {
      success: vi.fn(),
      error: vi.fn(),
      warning: vi.fn(),
      info: vi.fn()
    },
    ElLoading: {
      service: vi.fn(() => ({
        close: vi.fn()
      }))
    }
  }
})

// Mock 浏览器API
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
  }
})

Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn()
  }
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// Mock File API
global.File = class MockFile {
  constructor(bits, name, options = {}) {
    this.bits = bits
    this.name = name
    this.size = bits.reduce((acc, bit) => acc + bit.length, 0)
    this.type = options.type || ''
    this.lastModified = options.lastModified || Date.now()
  }
}

global.FileReader = class MockFileReader {
  constructor() {
    this.readyState = 0
    this.result = null
    this.error = null
    this.onload = null
    this.onerror = null
    this.onabort = null
  }
  
  readAsText(file) {
    setTimeout(() => {
      this.readyState = 2
      this.result = 'mock file content'
      if (this.onload) this.onload({ target: this })
    }, 10)
  }
  
  readAsDataURL(file) {
    setTimeout(() => {
      this.readyState = 2
      this.result = 'data:text/plain;base64,bW9jayBmaWxlIGNvbnRlbnQ='
      if (this.onload) this.onload({ target: this })
    }, 10)
  }
}

// Mock Crypto API
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: (arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    },
    subtle: {
      digest: vi.fn().mockResolvedValue(new ArrayBuffer(32))
    }
  }
})

// 测试工具函数
export const testUtils = {
  /**
   * 等待Vue组件更新
   */
  async waitForUpdate(wrapper, timeout = 1000) {
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 10))
  },

  /**
   * 模拟用户输入
   */
  async simulateUserInput(wrapper, selector, value) {
    const input = wrapper.find(selector)
    await input.setValue(value)
    await input.trigger('input')
    await this.waitForUpdate(wrapper)
  },

  /**
   * 模拟用户点击
   */
  async simulateUserClick(wrapper, selector) {
    const element = wrapper.find(selector)
    await element.trigger('click')
    await this.waitForUpdate(wrapper)
  },

  /**
   * 创建性能测试包装器
   */
  createPerformanceWrapper(fn) {
    return async (...args) => {
      const startTime = performance.now()
      const result = await fn(...args)
      const endTime = performance.now()
      
      return {
        result,
        duration: endTime - startTime,
        timestamp: Date.now()
      }
    }
  },

  /**
   * 生成测试数据
   */
  generateTestData(type, count = 10) {
    const generators = {
      rules: () => Array.from({ length: count }, (_, i) => ({
        id: i + 1,
        rule_key: `test-rule-${i + 1}`,
        rule_name: `测试规则${i + 1}`,
        status: i % 2 === 0 ? 'ACTIVE' : 'INACTIVE',
        type: ['data_quality', 'business_rule'][i % 2],
        level1: `一级错误${i + 1}`,
        level2: `二级错误${i + 1}`,
        level3: `三级错误${i + 1}`
      })),
      
      details: () => Array.from({ length: count }, (_, i) => ({
        id: i + 1,
        rule_name: `规则明细${i + 1}`,
        status: 'ACTIVE',
        error_reason: `错误原因${i + 1}`,
        degree: '严重',
        created_at: new Date().toISOString()
      }))
    }
    
    return generators[type] ? generators[type]() : []
  },

  /**
   * 模拟API延迟
   */
  async simulateApiDelay(min = 10, max = 100) {
    const delay = Math.random() * (max - min) + min
    await new Promise(resolve => setTimeout(resolve, delay))
  },

  /**
   * 验证性能指标
   */
  validatePerformanceMetrics(metrics, thresholds) {
    const results = {}
    
    for (const [key, threshold] of Object.entries(thresholds)) {
      const value = metrics[key]
      results[key] = {
        value,
        threshold,
        passed: value <= threshold,
        improvement: threshold > 0 ? ((threshold - value) / threshold * 100).toFixed(1) : 0
      }
    }
    
    return results
  },

  /**
   * 创建Mock Store
   */
  createMockStore(initialState = {}) {
    return {
      state: { ...initialState },
      getters: {},
      mutations: {},
      actions: {},
      commit: vi.fn(),
      dispatch: vi.fn()
    }
  }
}

// 性能测试助手
export const performanceHelpers = {
  /**
   * 测量函数执行时间
   */
  async measureExecutionTime(fn, iterations = 1) {
    const times = []
    
    for (let i = 0; i < iterations; i++) {
      const start = performance.now()
      await fn()
      const end = performance.now()
      times.push(end - start)
    }
    
    return {
      times,
      average: times.reduce((a, b) => a + b, 0) / times.length,
      min: Math.min(...times),
      max: Math.max(...times),
      median: times.sort((a, b) => a - b)[Math.floor(times.length / 2)]
    }
  },

  /**
   * 内存使用监控
   */
  measureMemoryUsage() {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      }
    }
    return null
  },

  /**
   * 创建性能基准
   */
  createBenchmark(name, fn) {
    return {
      name,
      run: async (iterations = 10) => {
        console.log(`🏃 运行性能基准: ${name}`)
        const result = await this.measureExecutionTime(fn, iterations)
        console.log(`📊 ${name} 结果:`, result)
        return result
      }
    }
  }
}

// 导出全局测试配置
export const testConfig = {
  timeout: 30000,
  retries: 2,
  verbose: process.env.VERBOSE_LOGGING === 'true',
  performance: {
    enabled: true,
    thresholds: {
      apiResponse: 100, // ms
      componentRender: 50, // ms
      memoryUsage: 100 * 1024 * 1024 // 100MB
    }
  }
}

console.log('✅ 测试环境设置完成')
