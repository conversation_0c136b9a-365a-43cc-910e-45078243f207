/**
 * 增强版规则明细API
 * 集成字段映射引擎、智能缓存和增强错误处理
 */

import { get, post, put, del } from './request'
import { fieldMappingEngine } from '../utils/fieldMappingEngine'
import { defaultApiCache } from '../utils/apiCache'
import { enhancedErrorHandler } from '../utils/enhancedErrorHandler'
import { performanceMonitor } from '../utils/performanceMonitor'
import type {
  EnhancedRequestConfig,
  EnhancedApiResponse,
  BatchOperationRequest,
  BatchOperationResponse,
  PaginationParams,
  PaginationResponse,
  SearchParams,
  StatisticsData
} from '../types/apiEnhanced'
import type {
  RuleDetail,
  CreateRuleDetailData,
  UpdateRuleDetailData
} from '../types/ruleDetails'

/**
 * 增强版API请求类
 */
class EnhancedRuleDetailsApi {
  private initialized = false

  /**
   * 初始化API
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      await fieldMappingEngine.initialize()
      this.initialized = true
      console.log('EnhancedRuleDetailsApi initialized successfully')
    } catch (error) {
      console.error('Failed to initialize EnhancedRuleDetailsApi:', error)
      throw error
    }
  }

  /**
   * 确保API已初始化
   */
  private ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error('EnhancedRuleDetailsApi not initialized. Call initialize() first.')
    }
  }

  /**
   * 执行增强API请求
   * @param config 请求配置
   * @returns 增强API响应
   */
  private async executeRequest<T>(config: EnhancedRequestConfig): Promise<EnhancedApiResponse<T>> {
    this.ensureInitialized()

    const startTime = Date.now()
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    try {
      // 1. 检查缓存
      if (config.enableCache && config.method === 'GET') {
        const cacheKey = config.cacheKey || this.generateCacheKey(config)
        const cachedData = defaultApiCache.get<T>(cacheKey)
        
        if (cachedData) {
          return {
            success: true,
            code: 200,
            message: '操作成功',
            data: cachedData,
            timestamp: Date.now(),
            requestId,
            fromCache: true,
            cacheKey,
            responseTime: Date.now() - startTime
          }
        }
      }

      // 2. 数据转换（请求）
      let requestData = config.data
      if (config.enableFieldMapping && requestData) {
        requestData = fieldMappingEngine.transformRequestData(requestData, config.ruleKey)
        
        // 数据验证
        if (config.validateRequest) {
          const validationResult = fieldMappingEngine.validateFields(requestData, config.ruleKey)
          if (!validationResult.valid) {
            throw new Error(`请求数据验证失败: ${validationResult.errors.join(', ')}`)
          }
        }
      }

      // 3. 执行请求
      let response: any
      switch (config.method) {
        case 'GET':
          response = await get(config.url, config.params)
          break
        case 'POST':
          response = await post(config.url, requestData)
          break
        case 'PUT':
          response = await put(config.url, requestData)
          break
        case 'DELETE':
          response = await del(config.url)
          break
        default:
          throw new Error(`Unsupported method: ${config.method}`)
      }

      // 4. 数据转换（响应）
      let responseData = response.data || response
      if (config.enableFieldMapping && responseData) {
        if (Array.isArray(responseData)) {
          responseData = responseData.map(item => 
            fieldMappingEngine.transformResponseData(item, config.ruleKey)
          )
        } else if (typeof responseData === 'object') {
          responseData = fieldMappingEngine.transformResponseData(responseData, config.ruleKey)
        }
      }

      // 5. 缓存响应数据
      if (config.enableCache && config.method === 'GET' && responseData) {
        const cacheKey = config.cacheKey || this.generateCacheKey(config)
        const cacheTtl = config.cacheTtl
        defaultApiCache.set(cacheKey, responseData, undefined, cacheTtl)
      }

      // 6. 构建增强响应
      const enhancedResponse: EnhancedApiResponse<T> = {
        success: response.success !== false,
        code: response.code || 200,
        message: response.message || '操作成功',
        data: responseData,
        timestamp: Date.now(),
        requestId,
        fromCache: false,
        responseTime: Date.now() - startTime
      }

      // 添加调试信息（开发环境）
      if ((import.meta as any).env?.DEV) {
        enhancedResponse.debug = {
          originalData: response.data || response,
          transformedData: responseData
        }
      }

      // 记录性能监控
      performanceMonitor.recordApiCall(
        config.url,
        Date.now() - startTime,
        enhancedResponse.fromCache || false,
        false
      )

      return enhancedResponse

    } catch (error: any) {
      // 记录错误性能监控
      performanceMonitor.recordApiCall(
        config.url,
        Date.now() - startTime,
        false,
        true
      )

      // 错误处理
      const enhancedError = enhancedErrorHandler.handleError(error, {
        requestId,
        url: config.url,
        method: config.method,
        ruleKey: config.ruleKey
      })

      // 自定义错误处理
      if (config.customErrorHandler) {
        config.customErrorHandler(enhancedError)
      }

      // 重新抛出错误
      throw enhancedError
    }
  }

  /**
   * 生成缓存键
   * @param config 请求配置
   * @returns 缓存键
   */
  private generateCacheKey(config: EnhancedRequestConfig): string {
    const parts = [
      config.method,
      config.url,
      config.params ? JSON.stringify(config.params) : '',
      config.ruleKey || ''
    ]
    return parts.join('|')
  }

  // ==================== 规则明细CRUD操作 ====================

  /**
   * 获取规则明细列表
   * @param ruleKey 规则键
   * @param params 查询参数
   * @returns 规则明细列表
   */
  async getRuleDetailsList(
    ruleKey: string, 
    params: PaginationParams & SearchParams = { page: 1, page_size: 20 }
  ): Promise<EnhancedApiResponse<PaginationResponse<RuleDetail>>> {
    return this.executeRequest({
      url: `/v1/rules/details/${ruleKey}`,
      method: 'GET',
      params,
      enableCache: true,
      enableFieldMapping: true,
      cacheKey: `rule_details_list_${ruleKey}_${JSON.stringify(params)}`,
      cacheTtl: 2 * 60 * 1000, // 2分钟缓存
      ruleKey
    })
  }

  /**
   * 获取单条规则明细
   * @param ruleKey 规则键
   * @param detailId 明细ID
   * @returns 规则明细详情
   */
  async getRuleDetailById(
    ruleKey: string, 
    detailId: string | number
  ): Promise<EnhancedApiResponse<RuleDetail>> {
    return this.executeRequest({
      url: `/v1/rules/details/${ruleKey}/${detailId}`,
      method: 'GET',
      enableCache: true,
      enableFieldMapping: true,
      cacheKey: `rule_detail_${ruleKey}_${detailId}`,
      cacheTtl: 5 * 60 * 1000, // 5分钟缓存
      ruleKey
    })
  }

  /**
   * 创建规则明细
   * @param ruleKey 规则键
   * @param data 规则明细数据
   * @returns 创建结果
   */
  async createRuleDetail(
    ruleKey: string, 
    data: CreateRuleDetailData
  ): Promise<EnhancedApiResponse<RuleDetail>> {
    const response = await this.executeRequest<RuleDetail>({
      url: `/v1/rules/details/${ruleKey}`,
      method: 'POST',
      data,
      enableFieldMapping: true,
      validateRequest: true,
      ruleKey
    })

    // 清除相关缓存
    this.invalidateCache(`rule_details_list_${ruleKey}_*`)

    return response
  }

  /**
   * 更新规则明细
   * @param ruleKey 规则键
   * @param detailId 明细ID
   * @param data 更新数据
   * @returns 更新结果
   */
  async updateRuleDetail(
    ruleKey: string, 
    detailId: string | number, 
    data: UpdateRuleDetailData
  ): Promise<EnhancedApiResponse<RuleDetail>> {
    const response = await this.executeRequest<RuleDetail>({
      url: `/v1/rules/details/${ruleKey}/${detailId}`,
      method: 'PUT',
      data,
      enableFieldMapping: true,
      validateRequest: true,
      ruleKey
    })

    // 清除相关缓存
    this.invalidateCache(`rule_detail_${ruleKey}_${detailId}`)
    this.invalidateCache(`rule_details_list_${ruleKey}_*`)

    return response
  }

  /**
   * 删除规则明细
   * @param ruleKey 规则键
   * @param detailId 明细ID
   * @returns 删除结果
   */
  async deleteRuleDetail(
    ruleKey: string, 
    detailId: string | number
  ): Promise<EnhancedApiResponse<{ deleted: boolean }>> {
    const response = await this.executeRequest<{ deleted: boolean }>({
      url: `/v1/rules/details/${ruleKey}/${detailId}`,
      method: 'DELETE',
      ruleKey
    })

    // 清除相关缓存
    this.invalidateCache(`rule_detail_${ruleKey}_${detailId}`)
    this.invalidateCache(`rule_details_list_${ruleKey}_*`)

    return response
  }

  // ==================== 批量操作 ====================

  /**
   * 批量操作规则明细
   * @param ruleKey 规则键
   * @param request 批量操作请求
   * @returns 批量操作结果
   */
  async batchOperateRuleDetails(
    ruleKey: string,
    request: BatchOperationRequest<CreateRuleDetailData | UpdateRuleDetailData>
  ): Promise<EnhancedApiResponse<BatchOperationResponse>> {
    const response = await this.executeRequest<BatchOperationResponse>({
      url: `/v1/rules/details/${ruleKey}/batch`,
      method: 'POST',
      data: request,
      enableFieldMapping: true,
      validateRequest: request.options?.validateAll !== false, // 默认验证，除非明确禁用
      ruleKey
    })

    // 清除相关缓存
    this.invalidateCache(`rule_details_list_${ruleKey}_*`)
    this.invalidateCache(`rule_detail_${ruleKey}_*`)

    return response
  }

  // ==================== 统计和分析 ====================

  /**
   * 获取规则明细统计信息
   * @param ruleKey 规则键
   * @returns 统计信息
   */
  async getRuleDetailsStatistics(ruleKey: string): Promise<EnhancedApiResponse<StatisticsData>> {
    return this.executeRequest({
      url: `/v1/rules/details/${ruleKey}/statistics`,
      method: 'GET',
      enableCache: true,
      cacheKey: `rule_details_stats_${ruleKey}`,
      cacheTtl: 10 * 60 * 1000, // 10分钟缓存
      ruleKey
    })
  }

  // ==================== 缓存管理 ====================

  /**
   * 清除缓存
   * @param pattern 匹配模式
   */
  private invalidateCache(pattern: string): void {
    defaultApiCache.invalidate(pattern)
  }

  /**
   * 获取缓存统计
   * @returns 缓存统计信息
   */
  getCacheStats() {
    return defaultApiCache.getStats()
  }

  /**
   * 清除所有缓存
   */
  clearAllCache(): void {
    defaultApiCache.clear()
  }

  // ==================== 性能监控 ====================

  /**
   * 获取性能指标
   */
  getPerformanceMetrics() {
    return performanceMonitor.getMetrics()
  }

  /**
   * 获取详细性能统计
   */
  getDetailedPerformanceStats() {
    return performanceMonitor.getDetailedStats()
  }

  /**
   * 检查性能健康状态
   */
  checkPerformanceHealth() {
    return performanceMonitor.checkHealth()
  }

  /**
   * 导出性能报告
   */
  exportPerformanceReport(): string {
    return performanceMonitor.exportReport()
  }

  /**
   * 重置性能统计
   */
  resetPerformanceStats(): void {
    performanceMonitor.reset()
  }
}

// 创建单例实例
export const enhancedRuleDetailsApi = new EnhancedRuleDetailsApi()

// 默认导出
export default enhancedRuleDetailsApi
