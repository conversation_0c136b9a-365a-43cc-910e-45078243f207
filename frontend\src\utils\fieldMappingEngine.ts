/**
 * 字段映射引擎
 * 基于field_mapping.json配置实现前后端字段的自动转换
 * 支持数据验证、类型转换和错误处理
 */

import type { FieldDefinition, ValidationResult, FieldMappingConfig } from '../types/apiEnhanced'

/**
 * 字段映射引擎类
 * 提供统一的字段转换、验证和错误处理功能
 */
export class FieldMappingEngine {
  private config: FieldMappingConfig | null = null
  private cache: Map<string, any> = new Map()
  private initialized = false

  /**
   * 初始化字段映射引擎
   * 加载field_mapping.json配置文件
   */
  async initialize(): Promise<void> {
    if (this.initialized) return

    try {
      // 在实际项目中，这里应该从服务器加载配置
      // 目前使用静态导入作为示例
      const response = await fetch('/data/field_mapping.json')
      if (!response.ok) {
        throw new Error(`Failed to load field mapping config: ${response.statusText}`)
      }
      
      this.config = await response.json()
      this.initialized = true
      
      console.log('FieldMappingEngine initialized successfully')
    } catch (error) {
      console.error('Failed to initialize FieldMappingEngine:', error)
      throw error
    }
  }

  /**
   * 确保引擎已初始化
   */
  private ensureInitialized(): void {
    if (!this.initialized || !this.config) {
      throw new Error('FieldMappingEngine not initialized. Call initialize() first.')
    }
  }

  /**
   * 获取字段定义
   * @param fieldName 字段名
   * @returns 字段定义或null
   */
  getFieldDefinition(fieldName: string): FieldDefinition | null {
    this.ensureInitialized()
    
    const cacheKey = `field_def_${fieldName}`
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    const { field_definitions } = this.config!
    
    // 先在通用字段中查找
    if (field_definitions.common_fields[fieldName]) {
      const definition = field_definitions.common_fields[fieldName]
      this.cache.set(cacheKey, definition)
      return definition
    }

    // 再在特定字段中查找
    if (field_definitions.specific_fields?.[fieldName]) {
      const definition = field_definitions.specific_fields[fieldName]
      this.cache.set(cacheKey, definition)
      return definition
    }

    this.cache.set(cacheKey, null)
    return null
  }

  /**
   * 获取字段的中文名称
   * @param fieldName 字段名
   * @returns 中文名称
   */
  getFieldChineseName(fieldName: string): string {
    const definition = this.getFieldDefinition(fieldName)
    return definition?.chinese_name || fieldName
  }

  /**
   * 获取字段的数据类型
   * @param fieldName 字段名
   * @returns 数据类型
   */
  getFieldDataType(fieldName: string): string {
    const definition = this.getFieldDefinition(fieldName)
    return definition?.data_type || 'string'
  }

  /**
   * 检查字段是否必填
   * @param fieldName 字段名
   * @returns 是否必填
   */
  isFieldRequired(fieldName: string): boolean {
    const definition = this.getFieldDefinition(fieldName)
    return definition?.required || false
  }

  /**
   * 获取字段验证规则
   * @param fieldName 字段名
   * @returns 验证规则数组
   */
  getFieldValidationRules(fieldName: string): string[] {
    const definition = this.getFieldDefinition(fieldName)
    return definition?.validation_rules || []
  }

  /**
   * 转换请求数据：前端 → 后端
   * @param data 前端数据
   * @param ruleKey 规则键（可选，用于特定字段处理）
   * @returns 转换后的后端数据
   */
  transformRequestData(data: Record<string, any>, _ruleKey?: string): Record<string, any> {
    this.ensureInitialized()

    const transformed: Record<string, any> = {}

    for (const [key, value] of Object.entries(data)) {
      const definition = this.getFieldDefinition(key)
      
      if (definition) {
        // 使用API字段名
        const apiFieldName = definition.api_field || key
        transformed[apiFieldName] = this.convertValue(value, definition.data_type)
      } else {
        // 未定义的字段直接传递
        transformed[key] = value
      }
    }

    return transformed
  }

  /**
   * 转换响应数据：后端 → 前端
   * @param data 后端数据
   * @param ruleKey 规则键（可选，用于特定字段处理）
   * @returns 转换后的前端数据
   */
  transformResponseData(data: Record<string, any>, _ruleKey?: string): Record<string, any> {
    this.ensureInitialized()

    const transformed: Record<string, any> = {}

    for (const [key, value] of Object.entries(data)) {
      const definition = this.getFieldDefinition(key)
      
      if (definition) {
        // 使用标准字段名
        transformed[key] = this.convertValue(value, definition.data_type)
      } else {
        // 未定义的字段直接传递
        transformed[key] = value
      }
    }

    return transformed
  }

  /**
   * 验证字段数据
   * @param data 待验证数据
   * @param ruleKey 规则键
   * @returns 验证结果
   */
  validateFields(data: Record<string, any>, _ruleKey?: string): ValidationResult {
    this.ensureInitialized()

    const errors: string[] = []
    const warnings: string[] = []

    // 获取所有字段定义
    const { field_definitions } = this.config!
    const allFields = { ...field_definitions.common_fields, ...field_definitions.specific_fields }

    // 检查必填字段是否缺失
    for (const [fieldName, definition] of Object.entries(allFields)) {
      if (definition.required && !(fieldName in data)) {
        errors.push(`${definition.chinese_name}不能为空`)
      }
    }

    // 验证传入的字段
    for (const [fieldName, value] of Object.entries(data)) {
      const definition = this.getFieldDefinition(fieldName)

      if (!definition) {
        warnings.push(`未知字段: ${fieldName}`)
        continue
      }

      const fieldErrors = this.validateFieldValue(fieldName, value, definition)
      errors.push(...fieldErrors)
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 验证单个字段值
   * @param fieldName 字段名
   * @param value 字段值
   * @param definition 字段定义
   * @returns 错误信息数组
   */
  private validateFieldValue(_fieldName: string, value: any, definition: FieldDefinition): string[] {
    const errors: string[] = []
    const chineseName = definition.chinese_name

    // 必填验证
    if (definition.required && (value === null || value === undefined || value === '')) {
      errors.push(`${chineseName}不能为空`)
    }

    // 类型验证
    if (value !== null && value !== undefined) {
      const typeError = this.validateDataType(value, definition.data_type, chineseName)
      if (typeError) {
        errors.push(typeError)
      }
    }

    // 长度验证
    if (definition.max_length && typeof value === 'string' && value.length > definition.max_length) {
      errors.push(`${chineseName}长度不能超过${definition.max_length}个字符`)
    }

    // 自定义验证规则
    if (definition.validation_rules) {
      for (const rule of definition.validation_rules) {
        const ruleError = this.validateRule(value, rule, chineseName)
        if (ruleError) {
          errors.push(ruleError)
        }
      }
    }

    return errors
  }

  /**
   * 验证数据类型
   * @param value 值
   * @param expectedType 期望类型
   * @param fieldName 字段名（用于错误提示）
   * @returns 错误信息或null
   */
  private validateDataType(value: any, expectedType: string, fieldName: string): string | null {
    switch (expectedType) {
      case 'string':
      case 'text':
        if (typeof value !== 'string') {
          return `${fieldName}必须为字符串类型`
        }
        break
      case 'integer':
        if (!Number.isInteger(Number(value))) {
          return `${fieldName}必须为整数`
        }
        break
      case 'array':
        if (!Array.isArray(value)) {
          return `${fieldName}必须为数组类型`
        }
        break
      case 'boolean':
        if (typeof value !== 'boolean') {
          return `${fieldName}必须为布尔类型`
        }
        break
    }
    return null
  }

  /**
   * 验证自定义规则
   * @param value 值
   * @param rule 规则
   * @param fieldName 字段名
   * @returns 错误信息或null
   */
  private validateRule(value: any, rule: string, fieldName: string): string | null {
    if (rule === 'required' && !value) {
      return `${fieldName}不能为空`
    }

    if (rule.startsWith('max_length:')) {
      const maxLength = parseInt(rule.split(':')[1])
      if (typeof value === 'string' && value.length > maxLength) {
        return `${fieldName}长度不能超过${maxLength}个字符`
      }
    }

    if (rule.startsWith('min:')) {
      const min = parseInt(rule.split(':')[1])
      if (Number(value) < min) {
        return `${fieldName}不能小于${min}`
      }
    }

    if (rule.startsWith('max:')) {
      const max = parseInt(rule.split(':')[1])
      if (Number(value) > max) {
        return `${fieldName}不能大于${max}`
      }
    }

    return null
  }

  /**
   * 转换值的数据类型
   * @param value 原始值
   * @param targetType 目标类型
   * @returns 转换后的值
   */
  private convertValue(value: any, targetType: string): any {
    if (value === null || value === undefined) {
      return value
    }

    switch (targetType) {
      case 'integer':
        return Number.isNaN(Number(value)) ? value : Number(value)
      case 'boolean':
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true'
        }
        return Boolean(value)
      case 'array':
        if (typeof value === 'string') {
          try {
            return JSON.parse(value)
          } catch {
            return value.split(',').map(item => item.trim())
          }
        }
        return Array.isArray(value) ? value : [value]
      default:
        return value
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 创建单例实例
export const fieldMappingEngine = new FieldMappingEngine()

// 默认导出
export default fieldMappingEngine
