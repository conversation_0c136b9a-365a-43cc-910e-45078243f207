import time
from typing import Any, Generic, TypeVar

from pydantic import BaseModel, Field

T = TypeVar("T")


class ApiResponse(BaseModel, Generic[T]):
    """
    API统一响应格式（增强版）

    统一的API响应格式，确保主从节点响应一致性
    所有API响应都应使用此格式
    """

    code: int = Field(200, description="业务状态码")
    success: bool = Field(True, description="操作成功标识")
    message: str = Field("操作成功", description="用户友好的提示信息")
    data: T | None = Field(None, description="实际数据内容")

    # 新增字段用于请求追踪和调试
    request_id: str | None = Field(None, description="请求追踪ID")
    timestamp: float | None = Field(None, description="响应时间戳")
    debug_info: dict[str, Any] | None = Field(None, description="调试信息（仅开发环境）")

    @classmethod
    def success_response(
        cls,
        data: T = None,
        message: str = "操作成功",
        request_id: str | None = None,
        debug_info: dict[str, Any] | None = None,
    ) -> "ApiResponse[T]":
        """
        创建成功响应

        Args:
            data: 响应数据
            message: 成功消息
            request_id: 请求追踪ID
            debug_info: 调试信息

        Returns:
            ApiResponse: 成功响应对象
        """
        return cls(
            code=200,
            success=True,
            message=message,
            data=data,
            request_id=request_id,
            timestamp=time.time(),
            debug_info=debug_info,
        )

    @classmethod
    def error_response(
        cls,
        code: int,
        message: str,
        data: T = None,
        request_id: str | None = None,
        debug_info: dict[str, Any] | None = None,
    ) -> "ApiResponse[T]":
        """
        创建错误响应

        Args:
            code: 错误状态码
            message: 错误消息
            data: 错误相关数据
            request_id: 请求追踪ID
            debug_info: 调试信息

        Returns:
            ApiResponse: 错误响应对象
        """
        return cls(
            code=code,
            success=False,
            message=message,
            data=data,
            request_id=request_id,
            timestamp=time.time(),
            debug_info=debug_info,
        )

    def model_dump(self, **kwargs) -> dict[str, Any]:
        """
        序列化响应对象，过滤调试信息（生产环境）

        Args:
            **kwargs: 序列化参数

        Returns:
            dict: 序列化后的字典
        """
        # 获取基础序列化结果
        result = super().model_dump(**kwargs)

        # 在生产环境中移除调试信息
        # 这里可以根据环境变量或配置来决定是否包含debug_info
        # 暂时保留debug_info，由调用方决定是否包含

        return result


class RuleVersionResponse(BaseModel):
    """Response model for the rule version API."""

    version: str = Field(..., description="A unique hash representing the current state of all active rules.")


class ExportedRuleData(BaseModel):
    """Represents a single rule's data in the export package."""

    base_rule: str = Field(..., description="The full python path to the base rule class.")
    data: list[dict[str, Any]] = Field(..., description="The dataset for this rule.")


class RulePackageResponse(BaseModel):
    """The complete package of all active rules exported for slave nodes."""

    version: str = Field(..., description="The version hash of this rule package.")
    rules: dict[str, ExportedRuleData] = Field(..., description="A dictionary of all active rules, keyed by rule_key.")


class ParsedRow(BaseModel):
    """Represents a single successfully parsed row from the uploaded file."""

    row_number: int
    data: dict[str, Any]


class FailedRow(BaseModel):
    """Represents a row that failed validation."""

    row_number: int
    error: str
    original_data: dict[str, Any]


class UploadPreviewResponse(BaseModel):
    """The response model for the file upload and preview API."""

    total_rows: int
    success_count: int
    failed_count: int
    parsed_rows: list[ParsedRow]
    failed_rows: list[FailedRow]


class ConfirmSubmissionRequest(BaseModel):
    """The request body for confirming a data submission."""

    user_id: str = Field(..., description="The ID of the user submitting the data.")
    data_to_submit: list[dict[str, Any]] = Field(..., description="The final list of data rows to be saved.")


class RegistrationTaskResponse(BaseModel):
    """规则注册任务响应"""

    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="状态消息")


class TaskStatusResponse(BaseModel):
    """任务状态查询响应"""

    task_id: str = Field(..., description="任务ID")
    task_type: str = Field(..., description="任务类型")
    rule_key: str = Field(..., description="规则键值")
    user_id: str | None = Field(None, description="用户ID")
    status: str = Field(..., description="任务状态")
    total_operations: int = Field(..., description="总操作数")
    completed_operations: int = Field(..., description="已完成操作数")
    progress_percentage: float = Field(..., description="进度百分比")
    current_message: str = Field(..., description="当前状态消息")
    error_message: str = Field(..., description="错误消息")
    result_data: dict[str, Any] | None = Field(None, description="结果数据")
    created_at: str | None = Field(None, description="创建时间")
    started_at: str | None = Field(None, description="开始时间")
    completed_at: str | None = Field(None, description="完成时间")
    execution_time: float = Field(..., description="执行时间（秒）")
    stats: dict[str, Any] = Field(..., description="统计信息")


class TaskListResponse(BaseModel):
    """任务列表响应"""

    tasks: list[TaskStatusResponse] = Field(..., description="任务列表")
    total_count: int = Field(..., description="总数量")
    page: int = Field(..., description="页码")
    page_size: int = Field(..., description="页大小")


# ===== 规则明细相关 API 模型 =====


# 旧的规则明细API模型已删除，使用标准化模型


# 旧的查询和更新请求模型已删除


# 旧的响应模型已删除


# 旧的批量操作模型已删除


# 旧的批量操作响应模型已删除


# ===== 增量操作相关模型 =====


class IncrementalOperation(BaseModel):
    """增量操作项"""

    operation: str = Field(..., description="操作类型: CREATE, UPDATE, DELETE")
    rule_detail_id: str = Field(..., description="规则明细ID")
    data: dict[str, Any] | None = Field(None, description="操作数据")


class IncrementalOperationRequest(BaseModel):
    """增量操作请求"""

    operations: list[IncrementalOperation] = Field(..., description="增量操作列表")


# ===== 兼容性相关模型 =====


class MigrationStatus(str):
    """迁移状态枚举"""

    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class DataFormat(str):
    """数据格式枚举"""

    JSON_LEGACY = "JSON_LEGACY"
    DETAILS_TABLE = "DETAILS_TABLE"
    HYBRID = "HYBRID"


class MigrationStatusResponse(BaseModel):
    """迁移状态响应"""

    rule_key: str = Field(..., description="规则键")
    migration_status: str = Field(..., description="迁移状态")
    migration_timestamp: str | None = Field(None, description="迁移时间戳")
    data_format: str = Field(..., description="数据格式")
    legacy_data_available: bool = Field(..., description="传统数据是否可用")
    details_data_available: bool = Field(..., description="明细数据是否可用")
    total_legacy_records: int = Field(..., description="传统记录总数")
    total_detail_records: int = Field(..., description="明细记录总数")
    consistency_check: str = Field(..., description="一致性检查结果")


# ============================================================================
# 标准化模型定义（使用标准字段名）
# ============================================================================


class RuleDetailStatus(str):
    """规则明细状态枚举"""

    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    DEPRECATED = "DEPRECATED"


class CreateRuleDetailRequest(BaseModel):
    """创建规则明细请求（使用标准字段名）"""

    rule_id: str = Field(..., min_length=1, max_length=100, description="规则ID")
    rule_name: str = Field(..., min_length=1, max_length=500, description="规则名称")

    # 使用标准字段名
    level1: str = Field(..., min_length=1, max_length=255, description="一级错误类型")
    level2: str = Field(..., min_length=1, max_length=255, description="二级错误类型")
    level3: str = Field(..., min_length=1, max_length=255, description="三级错误类型")
    error_reason: str = Field(..., min_length=1, description="错误原因")
    degree: str = Field(..., min_length=1, max_length=50, description="错误程度")
    reference: str = Field(..., min_length=1, description="质控依据或参考资料")
    detail_position: str = Field(..., min_length=1, max_length=100, description="具体位置描述")
    prompted_fields1: str = Field(..., min_length=1, max_length=100, description="提示字段编码")
    type: str = Field(..., min_length=1, max_length=100, description="规则类别")
    pos: str = Field(..., min_length=1, max_length=100, description="适用业务")
    applicableArea: str = Field(..., min_length=1, max_length=100, description="适用地区")
    default_use: str = Field(..., min_length=1, max_length=50, description="默认选用")
    start_date: str = Field(..., min_length=1, max_length=20, description="开始日期")
    end_date: str = Field(..., min_length=1, max_length=20, description="结束日期")

    # 可选字段
    prompted_fields3: str | None = Field(None, max_length=100, description="提示字段类型")
    remarks: str | None = Field(None, description="备注信息")
    in_illustration: str | None = Field(None, description="入参说明")

    # 固定的高频字段（数组类型字段接受列表格式）
    yb_code: list[str] | None = Field(None, description="药品编码列表")
    diag_whole_code: list[str] | None = Field(None, description="完整诊断编码列表")
    diag_code_prefix: list[str] | None = Field(None, description="诊断编码前缀列表")
    diag_name_keyword: str | None = Field(None, max_length=200, description="诊断名称关键字，逗号分隔")
    fee_whole_code: list[str] | None = Field(None, description="药品/诊疗项目完整编码列表")
    fee_code_prefix: list[str] | None = Field(None, description="药品/诊疗项目编码前缀列表")

    # 扩展字段
    extended_fields: str | None = Field(None, description="JSON格式的扩展字段")


class UpdateRuleDetailRequest(BaseModel):
    """更新规则明细请求（使用标准字段名）"""

    rule_name: str | None = Field(None, min_length=1, max_length=500, description="规则名称")

    # 使用标准字段名
    level1: str | None = Field(None, max_length=255, description="一级错误类型")
    level2: str | None = Field(None, max_length=255, description="二级错误类型")
    level3: str | None = Field(None, max_length=255, description="三级错误类型")
    error_reason: str | None = Field(None, description="错误原因")
    degree: str | None = Field(None, max_length=50, description="错误程度")
    reference: str | None = Field(None, description="质控依据或参考资料")
    detail_position: str | None = Field(None, max_length=100, description="具体位置描述")
    prompted_fields3: str | None = Field(None, max_length=100, description="提示字段类型")
    prompted_fields1: str | None = Field(None, max_length=100, description="提示字段编码")
    type: str | None = Field(None, max_length=100, description="规则类别")
    pos: str | None = Field(None, max_length=100, description="适用业务")
    applicableArea: str | None = Field(None, max_length=100, description="适用地区")
    default_use: str | None = Field(None, max_length=50, description="默认选用")
    remarks: str | None = Field(None, description="备注信息")
    in_illustration: str | None = Field(None, description="入参说明")
    start_date: str | None = Field(None, max_length=20, description="开始日期")
    end_date: str | None = Field(None, max_length=20, description="结束日期")

    # 固定的高频字段（数组类型字段接受列表格式）
    yb_code: list[str] | None = Field(None, description="药品编码列表")
    diag_whole_code: list[str] | None = Field(None, description="完整诊断编码列表")
    diag_code_prefix: list[str] | None = Field(None, description="诊断编码前缀列表")
    diag_name_keyword: str | None = Field(None, max_length=200, description="诊断名称关键字，逗号分隔")
    fee_whole_code: list[str] | None = Field(None, description="药品/诊疗项目完整编码列表")
    fee_code_prefix: list[str] | None = Field(None, description="药品/诊疗项目编码前缀列表")

    # 扩展字段
    extended_fields: str | None = Field(None, description="JSON格式的扩展字段")
    status: str | None = Field(None, description="状态")


class RuleDetailResponse(BaseModel):
    """规则明细响应（使用标准字段名）"""

    id: int = Field(..., description="明细ID")
    rule_id: str = Field(..., description="规则ID")
    rule_key: str = Field(..., description="规则模板类型")
    rule_name: str = Field(..., description="规则名称")

    # 使用标准字段名
    level1: str = Field(..., description="一级错误类型")
    level2: str = Field(..., description="二级错误类型")
    level3: str = Field(..., description="三级错误类型")
    error_reason: str = Field(..., description="错误原因")
    degree: str = Field(..., description="错误程度")
    reference: str = Field(..., description="质控依据或参考资料")
    detail_position: str = Field(..., description="具体位置描述")
    prompted_fields1: str = Field(..., description="提示字段编码")
    type: str = Field(..., description="规则类别")
    pos: str = Field(..., description="适用业务")
    applicableArea: str = Field(..., description="适用地区")
    default_use: str = Field(..., description="默认选用")
    start_date: str = Field(..., description="开始日期")
    end_date: str = Field(..., description="结束日期")

    # 可选字段
    prompted_fields3: str | None = Field(None, description="提示字段类型")
    remarks: str | None = Field(None, description="备注信息")
    in_illustration: str | None = Field(None, description="入参说明")

    # 固定的高频字段（统一为列表类型）
    yb_code: list[str] | None = Field(None, description="药品编码列表")
    diag_whole_code: list[str] | None = Field(None, description="完整诊断编码列表")
    diag_code_prefix: list[str] | None = Field(None, description="诊断编码前缀列表")
    diag_name_keyword: str | None = Field(None, description="诊断名称关键字，逗号分隔")  # 保持字符串类型
    fee_whole_code: list[str] | None = Field(None, description="药品/诊疗项目完整编码列表")
    fee_code_prefix: list[str] | None = Field(None, description="药品/诊疗项目编码前缀列表")

    # 扩展字段
    extended_fields: str | None = Field(None, description="JSON格式的扩展字段")

    # 元数据
    status: str = Field(..., description="记录状态")
    created_at: str | None = Field(None, description="创建时间")
    updated_at: str | None = Field(None, description="更新时间")


class PaginationResponse(BaseModel):
    """分页响应"""

    items: list[Any] = Field(..., description="数据项列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")


# ===== 批量操作相关模型 =====


class BatchOperationRequest(BaseModel):
    """批量操作请求"""

    operation: str = Field(..., description="操作类型: CREATE, UPDATE, DELETE")
    data: list[dict[str, Any]] = Field(..., description="操作数据列表")


class BatchOperationResponse(BaseModel):
    """批量操作响应"""

    success_count: int = Field(..., description="成功数量")
    failed_count: int = Field(..., description="失败数量")
    total_count: int = Field(..., description="总数量")
    errors: list[str] = Field(default_factory=list, description="错误信息列表")
