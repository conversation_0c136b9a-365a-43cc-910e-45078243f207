/**
 * 核心组件数据绑定优化测试
 * 验证组件的数据绑定、字段映射和类型安全
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { setActivePinia, createPinia } from 'pinia'
import { ElDrawer, ElTable, ElInput } from 'element-plus'

// Mock 字段映射函数
vi.mock('@/types/generated-fields', () => ({
  getFieldChineseName: vi.fn((field) => {
    const fieldMap = {
      'rule_name': '规则名称',
      'level1': '一级错误',
      'level2': '二级错误',
      'level3': '三级错误',
      'status': '状态'
    }
    return fieldMap[field] || field
  }),
  getRuleTypeChineseName: vi.fn((type) => {
    const typeMap = {
      'validation': '验证规则',
      'business': '业务规则'
    }
    return typeMap[type] || type
  }),
  validateFieldValue: vi.fn(() => ({ valid: true }))
}))

// Mock 字段映射引擎
vi.mock('@/utils/fieldMappingEngine', () => ({
  fieldMappingEngine: {
    transformToApi: vi.fn((data) => data),
    transformFromApi: vi.fn((data) => data)
  }
}))

// Mock Composables
vi.mock('@/composables/business/useRuleDetail', () => ({
  useRuleDetailDrawer: () => ({
    visible: { value: false },
    currentRule: { value: { rule_name: '测试规则', rule_key: 'test-rule' } },
    ruleSchema: { value: [] },
    ruleStatistics: { value: {} },
    detailLoading: { value: false },
    drawerTitle: { value: '规则详情' },
    ruleSchemaFormatted: { value: [] },
    ruleStatsFormatted: { value: [] },
    handleClose: vi.fn(),
    handleDownloadTemplate: vi.fn(),
    handleUploadData: vi.fn(),
    getTypeTagType: vi.fn(() => 'primary')
  })
}))

vi.mock('@/composables/business/useRuleDetailsManagement', () => ({
  useRuleDetailsManagement: () => ({
    detailsList: { value: [] },
    selectedDetails: { value: [] },
    detailsCount: { value: 0 },
    selectedCount: { value: 0 },
    hasSelected: { value: false },
    isLoading: { value: false },
    loadDetailsList: vi.fn(),
    refreshList: vi.fn(),
    deleteDetail: vi.fn(),
    performSearch: vi.fn(),
    clearSearch: vi.fn(),
    applyFilters: vi.fn(),
    resetFilters: vi.fn(),
    handleSelectionChange: vi.fn()
  })
}))

describe('核心组件数据绑定优化测试', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  describe('RuleDetailDrawer 数据绑定', () => {
    it('应该正确使用字段映射显示中文字段名', async () => {
      const { getFieldChineseName } = await import('@/types/generated-fields')
      
      // 验证字段映射函数被正确调用
      const result = getFieldChineseName('rule_name')
      expect(result).toBe('规则名称')
      
      // 验证不同字段的映射
      expect(getFieldChineseName('level1')).toBe('一级错误')
      expect(getFieldChineseName('level2')).toBe('二级错误')
      expect(getFieldChineseName('level3')).toBe('三级错误')
    })

    it('应该正确格式化规则类型', async () => {
      const { getRuleTypeChineseName } = await import('@/types/generated-fields')
      
      expect(getRuleTypeChineseName('validation')).toBe('验证规则')
      expect(getRuleTypeChineseName('business')).toBe('业务规则')
    })
  })

  describe('RuleDetailsCRUD 搜索和过滤优化', () => {
    it('应该使用字段映射生成搜索占位符', async () => {
      const { getFieldChineseName } = await import('@/types/generated-fields')
      
      // 模拟搜索占位符生成
      const ruleName = getFieldChineseName('rule_name')
      const level1 = getFieldChineseName('level1')
      const placeholder = `搜索${ruleName}、${level1}...`
      
      expect(placeholder).toBe('搜索规则名称、一级错误...')
    })

    it('应该正确处理过滤器选项', async () => {
      const { getFieldChineseName } = await import('@/types/generated-fields')
      
      // 验证过滤器选项使用字段映射
      const filterOptions = [
        { label: getFieldChineseName('level1'), value: 'level1' },
        { label: getFieldChineseName('level2'), value: 'level2' },
        { label: getFieldChineseName('level3'), value: 'level3' }
      ]
      
      expect(filterOptions[0].label).toBe('一级错误')
      expect(filterOptions[1].label).toBe('二级错误')
      expect(filterOptions[2].label).toBe('三级错误')
    })
  })

  describe('DataUploader 数据验证优化', () => {
    it('应该正确使用字段映射引擎验证数据', async () => {
      const { fieldMappingEngine } = await import('@/utils/fieldMappingEngine')
      const { validateFieldValue } = await import('@/types/generated-fields')
      
      const testData = { rule_name: '测试规则', level1: '错误信息' }
      
      // 验证数据转换
      fieldMappingEngine.transformToApi(testData)
      expect(fieldMappingEngine.transformToApi).toHaveBeenCalledWith(testData)
      
      // 验证字段值验证
      validateFieldValue('rule_name', '测试规则', 'string')
      expect(validateFieldValue).toHaveBeenCalledWith('rule_name', '测试规则', 'string')
    })

    it('应该正确处理数据验证错误', async () => {
      const { validateFieldValue, getFieldChineseName } = await import('@/types/generated-fields')
      
      // Mock 验证失败
      validateFieldValue.mockReturnValueOnce({ 
        valid: false, 
        error: '格式不正确' 
      })
      
      const fieldName = 'rule_name'
      const chineseName = getFieldChineseName(fieldName)
      const validationResult = validateFieldValue(fieldName, '', 'string')
      
      if (!validationResult.valid) {
        const errorMessage = `${chineseName}: ${validationResult.error}`
        expect(errorMessage).toBe('规则名称: 格式不正确')
      }
    })
  })

  describe('类型安全验证', () => {
    it('应该正确处理空值和未定义值', async () => {
      const { getFieldChineseName } = await import('@/types/generated-fields')
      
      // 测试空值处理
      expect(getFieldChineseName('')).toBe('')
      expect(getFieldChineseName(null)).toBe(null)
      expect(getFieldChineseName(undefined)).toBe(undefined)
    })

    it('应该正确处理数组和对象数据', async () => {
      const { fieldMappingEngine } = await import('@/utils/fieldMappingEngine')
      
      const arrayData = [{ rule_name: '规则1' }, { rule_name: '规则2' }]
      const objectData = { rules: arrayData, total: 2 }
      
      // 验证数组数据处理
      fieldMappingEngine.transformFromApi(arrayData)
      expect(fieldMappingEngine.transformFromApi).toHaveBeenCalledWith(arrayData)
      
      // 验证对象数据处理
      fieldMappingEngine.transformFromApi(objectData)
      expect(fieldMappingEngine.transformFromApi).toHaveBeenCalledWith(objectData)
    })
  })

  describe('性能优化验证', () => {
    it('应该正确缓存字段映射结果', async () => {
      const { getFieldChineseName } = await import('@/types/generated-fields')
      
      // 多次调用相同字段
      getFieldChineseName('rule_name')
      getFieldChineseName('rule_name')
      getFieldChineseName('rule_name')
      
      // 验证函数被调用（实际实现中应该有缓存机制）
      expect(getFieldChineseName).toHaveBeenCalledTimes(3)
    })

    it('应该正确处理大量数据的字段映射', async () => {
      const { fieldMappingEngine } = await import('@/utils/fieldMappingEngine')
      
      // 模拟大量数据
      const largeDataSet = Array.from({ length: 1000 }, (_, i) => ({
        rule_name: `规则${i}`,
        level1: `错误${i}`
      }))
      
      // 验证批量处理
      fieldMappingEngine.transformFromApi(largeDataSet)
      expect(fieldMappingEngine.transformFromApi).toHaveBeenCalledWith(largeDataSet)
    })
  })
})
