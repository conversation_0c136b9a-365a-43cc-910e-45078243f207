"""
Refactored Slave Node Entry Point

This file serves as the clean entry point for the slave node, handling only:
- Application initialization and configuration
- Startup/shutdown event management
- Router registration
- Service initialization

All API endpoints have been moved to separate router modules for better organization.
"""

import asyncio

import uvicorn
from fastapi import FastAPI

from api.dependencies.services import set_rule_service
from api.routers.common.validation_logic import unified_validation_worker
from api.routers.slave import slave_routers
from api.routers.slave.validation import set_request_queue
from config.settings import settings
from core.logging.logging_system import log as logger
from core.middleware.request_tracking import RequestIdMiddleware
from core.middleware.unified_error_handler import UnifiedErrorHandlingMiddleware
from services.rule_loader import load_rules_from_file
from services.rule_service import RuleService
from services.sync_service import RuleSyncService

# Global instances
sync_service: RuleSyncService | None = None
rule_service: RuleService | None = None
REQUEST_QUEUE: asyncio.Queue | None = None
WORKER_TASKS: list[asyncio.Task] = []


async def startup_event():
    """
    On startup, the slave node will:
    1. Initialize the rule service
    2. Initialize request queue and validation workers
    3. Attempt to load rules from the local file cache
    4. If loading fails and sync is enabled, perform an initial sync
    5. Start the background service to periodically sync rules from the master (if enabled)

    支持离线模式：当ENABLE_RULE_SYNC=False时，仅依赖本地规则文件启动
    """
    global sync_service, rule_service, REQUEST_QUEUE, WORKER_TASKS
    logger.info("Slave node starting up...")

    # Initialize services
    rule_service = RuleService()
    await rule_service.start()
    set_rule_service(rule_service)

    # Initialize request queue (与主节点完全一致)
    REQUEST_QUEUE = asyncio.Queue(maxsize=settings.QUEUE_MAX_SIZE)
    set_request_queue(REQUEST_QUEUE)

    # Start unified validation workers
    logger.info(f"Starting {settings.WORKER_COUNT} validation workers...")
    for i in range(settings.WORKER_COUNT):
        task = asyncio.create_task(unified_validation_worker(REQUEST_QUEUE, rule_service, f"slave-worker-{i}"))
        WORKER_TASKS.append(task)

    logger.info(f"Slave node started {len(WORKER_TASKS)} validation workers")

    # 检查同步配置
    sync_enabled = settings.ENABLE_RULE_SYNC

    if sync_enabled:
        logger.info("Rule synchronization is enabled")
        loop = asyncio.get_event_loop()
        sync_service = RuleSyncService(loop)
    else:
        logger.info("Rule synchronization is disabled (offline mode)")
        sync_service = None

    # Try to load rules from local cache
    loaded_from_cache = await load_rules_from_file()

    if not loaded_from_cache:
        if sync_enabled and sync_service and sync_service.client:
            logger.warning("No local rules found or failed to load. Performing initial sync.")
            try:
                # Fetch the version first to pass it to the download method
                remote_version_resp = await sync_service.client.get("/api/v1/rules/version")
                remote_version_resp.raise_for_status()
                response_data = remote_version_resp.json()

                # 处理统一响应格式
                if "data" in response_data and "version" in response_data["data"]:
                    remote_version = response_data["data"]["version"]
                elif "version" in response_data:
                    remote_version = response_data["version"]
                else:
                    logger.error(f"Invalid response format from master: {response_data}")
                    raise ValueError("Invalid response format: missing version field")

                await sync_service.download_and_apply_rules(remote_version)

            except Exception as e:
                # 特殊处理认证错误
                if hasattr(e, "response") and getattr(e.response, "status_code", None) == 401:
                    logger.critical(
                        f"Initial rule synchronization failed due to authentication error. "
                        f"Please check SLAVE_API_KEY configuration: {e}"
                    )
                else:
                    logger.critical(
                        "Initial rule synchronization failed, slave cannot start properly: {}", str(e), exc_info=True
                    )
        else:
            # 离线模式下没有本地规则文件
            logger.error("No local rules found and synchronization is disabled.")
            logger.error(
                "To enable offline mode, ensure a valid rules_cache.json.gz file exists in the working directory."
            )
            logger.error("You can also enable synchronization by setting ENABLE_RULE_SYNC=True")
            # 不抛出异常，允许服务启动但记录错误

    # Start the background periodic sync (only if enabled)
    if sync_service:
        await sync_service.start()
    else:
        logger.info("Skipping sync service startup (offline mode)")


async def shutdown_event():
    """On shutdown, gracefully stop the sync service, validation workers and rule service."""
    global sync_service, rule_service, WORKER_TASKS
    logger.info("Slave node shutting down...")

    # Stop validation workers (与主节点一致)
    logger.info("Stopping validation workers...")
    for task in WORKER_TASKS:
        task.cancel()

    if WORKER_TASKS:
        await asyncio.gather(*WORKER_TASKS, return_exceptions=True)
    logger.info("All validation workers stopped.")

    # Stop services
    if rule_service:
        await rule_service.stop()
        rule_service.close()
        logger.info("Rule service stopped.")

    if sync_service:
        await sync_service.stop()
        logger.info("Sync service stopped.")


def create_slave_app() -> FastAPI:
    """
    Create and configure the slave FastAPI application.

    Returns:
        FastAPI: Configured slave application
    """
    app = FastAPI(
        title="Rule Slave Node",
        description="Performs high-speed rule validation based on rules synced from a master node.",
        version="1.0.0",
    )

    # Add request tracking middleware
    app.add_middleware(RequestIdMiddleware)

    # Register unified error handlers (same as master node)
    UnifiedErrorHandlingMiddleware.register_handlers(app)

    # Add event handlers
    app.add_event_handler("startup", startup_event)
    app.add_event_handler("shutdown", shutdown_event)

    # Include all slave routers
    for router in slave_routers:
        app.include_router(router)

    logger.info("Slave FastAPI application created with unified error handling")
    return app


# Create the app instance
app = create_slave_app()


if __name__ == "__main__":
    # This allows running the slave node directly for development
    uvicorn.run("slave:app", host="0.0.0.0", port=8080, reload=True)
