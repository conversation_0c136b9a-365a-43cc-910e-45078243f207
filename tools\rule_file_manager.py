#!/usr/bin/env python3
"""
规则文件管理工具

用于离线环境下的规则文件导入/导出操作，支持：
1. 从主节点导出规则文件
2. 验证规则文件格式和完整性
3. 手动导入规则文件到子节点
4. 规则文件格式转换
"""

import argparse
import asyncio
import gzip
import json
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.db_session import get_session_factory
from core.logging.logging_system import log as logger
from services.rule_loader import load_rules_from_db, load_rules_from_file


class RuleFileManager:
    """规则文件管理器"""

    def __init__(self):
        self.default_export_path = "rules_cache.json.gz"
        self.default_import_path = "rules_cache.json.gz"

    async def export_rules_from_db(self, output_path: str | None = None) -> bool:
        """
        从数据库导出规则到文件

        Args:
            output_path: 输出文件路径，默认为rules_cache.json.gz

        Returns:
            bool: 导出是否成功
        """
        if output_path is None:
            output_path = self.default_export_path

        try:
            logger.info(f"Starting rule export from database to {output_path}")

            # 从数据库加载规则
            session_factory = get_session_factory()
            with session_factory() as session:
                db_rules = load_rules_from_db(session)

            if not db_rules:
                logger.error("No rules found in database")
                return False

            # 序列化规则数据
            exported_rule_datasets = []
            for rule_dataset in db_rules:
                try:
                    exported_rule_datasets.append(rule_dataset.to_dict())
                except Exception as e:
                    logger.error(f"Failed to serialize rule dataset {rule_dataset.id}: {e}")
                    continue

            # 计算版本哈希
            import hashlib

            rule_keys = sorted([rule.base_rule.rule_key for rule in db_rules if rule.base_rule])
            version_string = "".join(rule_keys)
            version_hash = hashlib.sha256(version_string.encode("utf-8")).hexdigest()

            # 创建导出包
            package = {
                "version": version_hash,
                "rule_datasets": exported_rule_datasets,
                "export_timestamp": datetime.now().isoformat(),
                "total_count": len(exported_rule_datasets),
                "export_source": "database",
                "export_tool": "rule_file_manager",
            }

            # 写入文件
            json_data = json.dumps(package, ensure_ascii=False, indent=2)

            # 创建临时文件
            temp_path = f"{output_path}.tmp"
            with gzip.open(temp_path, "wt", encoding="utf-8") as f:
                f.write(json_data)

            # 原子性替换
            os.replace(temp_path, output_path)

            logger.info(f"Successfully exported {len(exported_rule_datasets)} rule datasets to {output_path}")
            logger.info(f"Package version: {version_hash[:8]}")
            logger.info(f"File size: {os.path.getsize(output_path)} bytes")

            return True

        except Exception as e:
            logger.error(f"Failed to export rules: {e}", exc_info=True)
            return False

    async def validate_rule_file(self, file_path: str) -> dict:
        """
        验证规则文件的格式和完整性

        Args:
            file_path: 规则文件路径

        Returns:
            Dict: 验证结果
        """
        result = {"valid": False, "errors": [], "warnings": [], "info": {}}

        try:
            if not os.path.exists(file_path):
                result["errors"].append(f"File not found: {file_path}")
                return result

            # 尝试读取文件
            try:
                with gzip.open(file_path, "rt", encoding="utf-8") as f:
                    package_data = json.load(f)
                result["info"]["compression"] = "gzip"
            except (OSError, gzip.BadGzipFile):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        package_data = json.load(f)
                    result["info"]["compression"] = "none"
                    result["warnings"].append("File is not gzip compressed")
                except Exception as e:
                    result["errors"].append(f"Failed to read file as JSON: {e}")
                    return result

            # 验证格式
            if not isinstance(package_data, dict):
                result["errors"].append("Root element must be a dictionary")
                return result

            # 检查必需字段
            required_fields = ["version", "rule_datasets"]
            for field in required_fields:
                if field not in package_data:
                    result["errors"].append(f"Missing required field: {field}")

            if result["errors"]:
                return result

            # 验证rule_datasets
            rule_datasets = package_data["rule_datasets"]
            if not isinstance(rule_datasets, list):
                result["errors"].append("rule_datasets must be a list")
                return result

            result["info"]["total_count"] = len(rule_datasets)
            result["info"]["version"] = package_data.get("version", "unknown")
            result["info"]["export_timestamp"] = package_data.get("export_timestamp")
            result["info"]["file_size"] = os.path.getsize(file_path)

            # 验证每个rule_dataset
            valid_datasets = 0
            for i, dataset in enumerate(rule_datasets):
                if not isinstance(dataset, dict):
                    result["warnings"].append(f"Rule dataset {i} is not a dictionary")
                    continue

                # 检查必需字段
                required_dataset_fields = ["id", "base_rule_id", "data_set", "base_rule"]
                missing_fields = [f for f in required_dataset_fields if f not in dataset]
                if missing_fields:
                    result["warnings"].append(f"Rule dataset {i} missing fields: {missing_fields}")
                    continue

                valid_datasets += 1

            result["info"]["valid_datasets"] = valid_datasets

            # 检查数据完整性
            declared_count = package_data.get("total_count")
            if declared_count is not None and declared_count != len(rule_datasets):
                result["warnings"].append(f"Count mismatch: declared {declared_count}, actual {len(rule_datasets)}")

            if valid_datasets == 0:
                result["errors"].append("No valid rule datasets found")
            else:
                result["valid"] = True
                if valid_datasets < len(rule_datasets):
                    result["warnings"].append(f"Only {valid_datasets}/{len(rule_datasets)} datasets are valid")

        except Exception as e:
            result["errors"].append(f"Validation failed: {e}")

        return result

    async def import_rule_file(self, file_path: str) -> bool:
        """
        导入规则文件（测试加载）

        Args:
            file_path: 规则文件路径

        Returns:
            bool: 导入是否成功
        """
        try:
            logger.info(f"Testing rule file import: {file_path}")

            # 首先验证文件
            validation_result = await self.validate_rule_file(file_path)
            if not validation_result["valid"]:
                logger.error("File validation failed:")
                for error in validation_result["errors"]:
                    logger.error(f"  - {error}")
                return False

            # 显示警告
            for warning in validation_result["warnings"]:
                logger.warning(f"  - {warning}")

            # 备份当前文件（如果存在）
            target_path = self.default_import_path
            backup_path = f"{target_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            if os.path.exists(target_path):
                os.rename(target_path, backup_path)
                logger.info(f"Backed up existing file to {backup_path}")

            # 复制新文件
            import shutil

            shutil.copy2(file_path, target_path)

            # 测试加载
            success = await load_rules_from_file()

            if success:
                logger.info("Rule file imported and loaded successfully")
                # 删除备份文件
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                return True
            else:
                logger.error("Failed to load imported rule file")
                # 恢复备份
                if os.path.exists(backup_path):
                    os.rename(backup_path, target_path)
                    logger.info("Restored backup file")
                return False

        except Exception as e:
            logger.error(f"Failed to import rule file: {e}", exc_info=True)
            return False


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="规则文件管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # 导出命令
    export_parser = subparsers.add_parser("export", help="从数据库导出规则文件")
    export_parser.add_argument("-o", "--output", help="输出文件路径", default="rules_cache.json.gz")

    # 验证命令
    validate_parser = subparsers.add_parser("validate", help="验证规则文件")
    validate_parser.add_argument("file", help="要验证的规则文件路径")

    # 导入命令
    import_parser = subparsers.add_parser("import", help="导入规则文件")
    import_parser.add_argument("file", help="要导入的规则文件路径")

    # 信息命令
    info_parser = subparsers.add_parser("info", help="显示规则文件信息")
    info_parser.add_argument("file", help="规则文件路径")

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    manager = RuleFileManager()

    if args.command == "export":
        success = await manager.export_rules_from_db(args.output)
        sys.exit(0 if success else 1)

    elif args.command == "validate":
        result = await manager.validate_rule_file(args.file)

        print(f"Validation result for {args.file}:")
        print(f"Valid: {result['valid']}")

        if result["errors"]:
            print("\nErrors:")
            for error in result["errors"]:
                print(f"  - {error}")

        if result["warnings"]:
            print("\nWarnings:")
            for warning in result["warnings"]:
                print(f"  - {warning}")

        if result["info"]:
            print("\nFile Information:")
            for key, value in result["info"].items():
                print(f"  {key}: {value}")

        sys.exit(0 if result["valid"] else 1)

    elif args.command == "import":
        success = await manager.import_rule_file(args.file)
        sys.exit(0 if success else 1)

    elif args.command == "info":
        result = await manager.validate_rule_file(args.file)

        print(f"File Information for {args.file}:")
        if result["info"]:
            for key, value in result["info"].items():
                print(f"  {key}: {value}")
        else:
            print("  No information available")

        if result["errors"]:
            print("\nErrors found:")
            for error in result["errors"]:
                print(f"  - {error}")


if __name__ == "__main__":
    asyncio.run(main())
