/**
 * 用户体验验证测试
 * 验证加载状态、错误提示、交互反馈等用户体验要素
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { setActivePinia, createPinia } from 'pinia'
import { ElMessage, ElLoading } from 'element-plus'

// 组件导入
import RuleDetailDrawer from '@/components/business/RuleDetailDrawer.vue'
import RuleDetailsTable from '@/components/business/RuleDetailsTable.vue'
import RuleDetailForm from '@/components/business/RuleDetailForm.vue'
import DataUploader from '@/views/DataUploader.vue'

// Store导入
import { useRuleDetailsStore } from '@/stores/ruleDetails'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElLoading: {
    service: vi.fn(() => ({
      close: vi.fn()
    }))
  }
}))

// Mock API
vi.mock('@/api/enhancedRuleDetailsApi', () => ({
  enhancedRuleDetailsApi: {
    getDetailsList: vi.fn(),
    getDetailById: vi.fn(),
    createDetail: vi.fn(),
    updateDetail: vi.fn(),
    deleteDetail: vi.fn(),
    searchDetails: vi.fn()
  }
}))

// Mock 字段映射
vi.mock('@/types/generated-fields', () => ({
  getFieldChineseName: vi.fn((field) => {
    const fieldMap = {
      'rule_name': '规则名称',
      'status': '状态',
      'type': '规则类别'
    }
    return fieldMap[field] || field
  }),
  getRuleTypeChineseName: vi.fn((type) => type),
  validateFieldValue: vi.fn(() => ({ valid: true }))
}))

describe('用户体验验证测试', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    vi.clearAllMocks()
  })

  describe('加载状态测试', () => {
    it('应该正确显示加载状态', async () => {
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')
      
      // Mock 延迟响应
      enhancedRuleDetailsApi.getDetailsList.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({
          items: [],
          total: 0
        }), 100))
      )

      const store = useRuleDetailsStore()
      
      // 开始加载
      const loadPromise = store.fetchDetailsList('test-rule')
      
      // 验证加载状态
      expect(store.loading).toBe(true)
      
      // 等待加载完成
      await loadPromise
      
      // 验证加载状态重置
      expect(store.loading).toBe(false)
    })

    it('应该在组件中正确显示加载指示器', async () => {
      const wrapper = mount(RuleDetailsTable, {
        props: {
          ruleKey: 'test-rule',
          loading: true
        },
        global: {
          plugins: [pinia],
          stubs: {
            'el-table': {
              template: '<div class="el-table" :loading="$attrs.loading"><slot /></div>',
              props: ['loading']
            },
            'el-card': true,
            'el-pagination': true
          }
        }
      })

      expect(wrapper.find('.el-table').attributes('loading')).toBe('true')
    })
  })

  describe('错误处理和提示测试', () => {
    it('应该正确处理API错误并显示用户友好的提示', async () => {
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')
      
      // Mock API错误
      enhancedRuleDetailsApi.getDetailsList.mockRejectedValue(
        new Error('网络连接失败')
      )

      const store = useRuleDetailsStore()
      
      try {
        await store.fetchDetailsList('test-rule')
      } catch (error) {
        // 错误应该被捕获
      }

      // 验证错误状态
      expect(store.loading).toBe(false)
    })

    it('应该显示表单验证错误', async () => {
      const { validateFieldValue } = await import('@/types/generated-fields')
      
      // Mock 验证失败
      validateFieldValue.mockReturnValue({
        valid: false,
        error: '字段不能为空'
      })

      const wrapper = mount(RuleDetailForm, {
        props: {
          modelValue: true,
          ruleKey: 'test-rule',
          mode: 'create'
        },
        global: {
          plugins: [pinia],
          stubs: {
            'el-dialog': true,
            'el-form': true,
            'el-form-item': true,
            'el-input': true,
            'el-select': true,
            'el-button': true
          }
        }
      })

      // 模拟表单提交
      await wrapper.vm.handleSubmit?.()

      // 验证错误处理
      expect(validateFieldValue).toHaveBeenCalled()
    })
  })

  describe('交互反馈测试', () => {
    it('应该提供即时的操作反馈', async () => {
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')
      
      enhancedRuleDetailsApi.createDetail.mockResolvedValue({
        id: 1,
        rule_name: '新规则'
      })

      const store = useRuleDetailsStore()
      
      await store.createDetail('test-rule', { rule_name: '新规则' })
      
      // 验证成功消息
      expect(ElMessage.success).toHaveBeenCalled()
    })

    it('应该正确处理选择操作反馈', async () => {
      const wrapper = mount(RuleDetailsTable, {
        props: {
          ruleKey: 'test-rule',
          loading: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'el-table': {
              template: '<div class="el-table"><slot /></div>',
              methods: {
                toggleRowSelection: vi.fn(),
                clearSelection: vi.fn()
              }
            },
            'el-card': true,
            'el-pagination': true,
            'el-button': true
          }
        }
      })

      // 模拟选择变更
      const mockSelection = [{ id: 1, rule_name: '测试规则' }]
      await wrapper.vm.handleSelectionChange?.(mockSelection)

      // 验证选择事件
      expect(wrapper.emitted('selection:change')).toBeTruthy()
    })
  })

  describe('响应式布局测试', () => {
    it('应该在不同屏幕尺寸下正确显示', async () => {
      // 模拟不同的视口尺寸
      const viewports = [
        { width: 1920, height: 1080 }, // 桌面
        { width: 1024, height: 768 },  // 平板
        { width: 375, height: 667 }    // 手机
      ]

      for (const viewport of viewports) {
        // 设置视口尺寸
        Object.defineProperty(window, 'innerWidth', {
          writable: true,
          configurable: true,
          value: viewport.width
        })
        Object.defineProperty(window, 'innerHeight', {
          writable: true,
          configurable: true,
          value: viewport.height
        })

        const wrapper = mount(RuleDetailDrawer, {
          props: {
            modelValue: true,
            ruleKey: 'test-rule'
          },
          global: {
            plugins: [pinia],
            stubs: {
              'el-drawer': {
                template: '<div class="el-drawer" :class="{ mobile: $attrs.size === \'100%\' }"><slot /></div>',
                props: ['size']
              },
              'el-card': true,
              'el-descriptions': true,
              'el-table': true
            }
          }
        })

        expect(wrapper.exists()).toBe(true)
      }
    })
  })

  describe('可访问性测试', () => {
    it('应该提供正确的ARIA标签', async () => {
      const wrapper = mount(RuleDetailsTable, {
        props: {
          ruleKey: 'test-rule',
          loading: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'el-table': {
              template: '<table role="table" aria-label="规则明细表格"><slot /></table>'
            },
            'el-card': true,
            'el-pagination': true
          }
        }
      })

      const table = wrapper.find('[role="table"]')
      expect(table.exists()).toBe(true)
      expect(table.attributes('aria-label')).toBe('规则明细表格')
    })

    it('应该支持键盘导航', async () => {
      const wrapper = mount(RuleDetailForm, {
        props: {
          modelValue: true,
          ruleKey: 'test-rule',
          mode: 'create'
        },
        global: {
          plugins: [pinia],
          stubs: {
            'el-dialog': true,
            'el-form': {
              template: '<form tabindex="0"><slot /></form>'
            },
            'el-form-item': true,
            'el-input': {
              template: '<input tabindex="0" />'
            },
            'el-button': {
              template: '<button tabindex="0"><slot /></button>'
            }
          }
        }
      })

      const form = wrapper.find('form')
      expect(form.attributes('tabindex')).toBe('0')
    })
  })

  describe('数据一致性测试', () => {
    it('应该保持数据状态一致性', async () => {
      const store = useRuleDetailsStore()
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')
      
      // Mock 数据
      const mockData = {
        items: [
          { id: 1, rule_name: '规则1', status: 'ACTIVE' },
          { id: 2, rule_name: '规则2', status: 'INACTIVE' }
        ],
        total: 2
      }
      
      enhancedRuleDetailsApi.getDetailsList.mockResolvedValue(mockData)
      
      await store.fetchDetailsList('test-rule')
      
      // 验证数据一致性
      expect(store.detailsList).toEqual(mockData.items)
      expect(store.pagination.total).toBe(mockData.total)
    })

    it('应该正确同步选择状态', async () => {
      const store = useRuleDetailsStore()
      
      const testItems = [
        { id: 1, rule_name: '规则1' },
        { id: 2, rule_name: '规则2' }
      ]
      
      // 设置选择
      store.setSelectedDetails(testItems)
      
      expect(store.selectedDetails).toEqual(testItems)
      expect(store.selectedCount).toBe(2)
      expect(store.hasSelected).toBe(true)
    })
  })

  describe('性能感知测试', () => {
    it('应该在合理时间内响应用户操作', async () => {
      const store = useRuleDetailsStore()
      const { enhancedRuleDetailsApi } = await import('@/api/enhancedRuleDetailsApi')
      
      enhancedRuleDetailsApi.getDetailsList.mockResolvedValue({
        items: [],
        total: 0
      })
      
      const startTime = performance.now()
      await store.fetchDetailsList('test-rule')
      const endTime = performance.now()
      
      const duration = endTime - startTime
      expect(duration).toBeLessThan(100) // 用户感知的响应时间应小于100ms
    })

    it('应该提供流畅的滚动和分页体验', async () => {
      const wrapper = mount(RuleDetailsTable, {
        props: {
          ruleKey: 'test-rule',
          loading: false
        },
        global: {
          plugins: [pinia],
          stubs: {
            'el-table': true,
            'el-card': true,
            'el-pagination': {
              template: '<div class="el-pagination" @current-change="$emit(\'current-change\', $event)"></div>',
              emits: ['current-change']
            }
          }
        }
      })

      const pagination = wrapper.find('.el-pagination')
      expect(pagination.exists()).toBe(true)
      
      // 模拟分页操作
      await pagination.trigger('current-change', 2)
      
      // 验证分页事件
      expect(wrapper.emitted('pagination:change')).toBeTruthy()
    })
  })
})
