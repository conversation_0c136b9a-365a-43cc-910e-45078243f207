<template>
  <div class="rule-details-table">
    <!-- 智能选择面板 -->
    <BatchSelectionPanel
      v-if="showSelectionPanel"
      :data-list="detailsList"
      :selected-items="selectedDetails"
      :max-selection="maxSelection"
      :default-expanded="false"
      @select-all="handleSelectAll"
      @select-none="handleSelectNone"
      @select-inverse="handleSelectInverse"
      @select-by-condition="handleSelectByCondition"
    />

    <el-card shadow="never" class="table-card">
      <!-- 表格头部信息 -->
      <div class="table-header">
        <div class="table-info">
          <span class="info-text">
            共 {{ pagination.total }} 条记录，当前第 {{ pagination.page }} 页
          </span>
          <span v-if="selectedDetails.length > 0" class="selection-info">
            已选择 <span class="selection-count">{{ selectedDetails.length }}</span> 项
          </span>
        </div>
        <div class="table-actions">
          <el-button
            size="small"
            :type="showSelectionPanel ? 'primary' : ''"
            :icon="Operation"
            @click="toggleSelectionPanel"
          >
            {{ showSelectionPanel ? '隐藏' : '显示' }}智能选择
          </el-button>
        </div>
      </div>

      <!-- 表格视图 -->
      <div class="table-view">
        <el-table
          ref="tableRef"
          :data="detailsList"
          :loading="loading"
          @selection-change="handleSelectionChange"
          @sort-change="handleSortChange"
          @row-click="handleRowClick"
          stripe
          border
          style="width: 100%"
          height="600"
          :row-class-name="getRowClassName"
        >
          <!-- 选择列 -->
          <el-table-column type="selection" width="55" align="center">
            <template #header>
              <div class="selection-header">
                <el-checkbox
                  :model-value="isAllSelected"
                  :indeterminate="isIndeterminate"
                  @change="handleSelectAllChange"
                />
                <el-dropdown
                  trigger="click"
                  size="small"
                  @command="handleSelectionCommand"
                >
                  <el-button
                    size="small"
                    text
                    :icon="ArrowDown"
                    style="margin-left: 4px;"
                  />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="select-all">全选当前页</el-dropdown-item>
                      <el-dropdown-item command="select-none">清空选择</el-dropdown-item>
                      <el-dropdown-item command="select-inverse">反选当前页</el-dropdown-item>
                      <el-dropdown-item command="select-visible" divided>选择可见项</el-dropdown-item>
                      <el-dropdown-item command="select-active">选择激活项</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>

          <!-- 序号列 -->
          <el-table-column type="index" label="序号" width="80" align="center" />

          <!-- 规则名称 -->
          <el-table-column
            prop="rule_name"
            :label="getFieldChineseName('rule_name')"
            min-width="200"
            show-overflow-tooltip
            sortable="custom"
          >
            <template #default="{ row }">
              <div class="rule-name-cell">
                <span class="rule-name">{{ row.rule_name }}</span>
                <el-tag
                  v-if="row.default_use"
                  size="small"
                  type="success"
                  class="default-tag"
                >
                  默认
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- 错误级别 -->
          <el-table-column label="错误级别" width="150" align="center">
            <template #default="{ row }">
              <div class="error-level-cell">
                <el-tag
                  v-if="row.level1"
                  size="small"
                  type="danger"
                  :title="`${getFieldChineseName('level1')}: ${row.level1}`"
                >
                  {{ row.level1 }}
                </el-tag>
                <el-tag
                  v-if="row.level2"
                  size="small"
                  type="warning"
                  class="level-tag"
                  :title="`${getFieldChineseName('level2')}: ${row.level2}`"
                >
                  {{ row.level2 }}
                </el-tag>
                <el-tag
                  v-if="row.level3"
                  size="small"
                  type="info"
                  class="level-tag"
                  :title="`${getFieldChineseName('level3')}: ${row.level3}`"
                >
                  {{ row.level3 }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- 规则类别 -->
          <el-table-column
            prop="type"
            :label="getFieldChineseName('type')"
            width="120"
            show-overflow-tooltip
            sortable="custom"
          >
            <template #default="{ row }">
              <el-tag
                v-if="row.type"
                size="small"
                type="primary"
              >
                {{ getRuleTypeChineseName(row.type) || row.type }}
              </el-tag>
              <span v-else class="empty-value">-</span>
            </template>
          </el-table-column>

          <!-- 状态 -->
          <el-table-column label="状态" width="100" align="center">
            <template #default="{ row }">
              <StatusTag :status="row.status" />
            </template>
          </el-table-column>

          <!-- 更新时间 -->
          <el-table-column
            prop="updated_at"
            label="更新时间"
            width="180"
            sortable="custom"
          >
            <template #default="{ row }">
              {{ formatDate(row.updated_at) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="warning"
                  size="small"
                  :icon="Edit"
                  @click.stop="handleEdit(row)"
                  :disabled="row.status === 'DELETED'"
                >
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  @click.stop="handleDelete(row)"
                  :disabled="row.status === 'DELETED'"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import {
  Edit,
  Delete,
  Operation,
  ArrowDown
} from '@element-plus/icons-vue'
import StatusTag from '../common/StatusTag.vue'
import BatchSelectionPanel from './BatchSelectionPanel.vue'
import { formatDate } from '../../utils/dateUtils'
import { useRuleDetailsStore } from '../../stores/ruleDetails'
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus'
import { getFieldChineseName, getRuleTypeChineseName } from '../../types/generated-fields'

// Props
const props = defineProps({
  ruleKey: {
    type: String,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  // 最大选择数量限制
  maxSelection: {
    type: Number,
    default: 0 // 0 表示无限制
  },
  // 是否显示智能选择面板
  showSmartSelection: {
    type: Boolean,
    default: true
  }
})

// Emits - 统一事件命名规范
const emit = defineEmits([
  'detail:view',      // 查看详情
  'detail:edit',      // 编辑详情
  'detail:delete',    // 删除详情
  'selection:change', // 选择变更
  'table:sort',       // 排序变更
  'pagination:change', // 分页变更
  'row:click',        // 行点击
  'error'             // 错误事件
])

// Store
const ruleDetailsStore = useRuleDetailsStore()
const {
  detailsList,
  selectedDetails,
  pagination
} = storeToRefs(ruleDetailsStore)

// 本地状态
const tableRef = ref()
const showSelectionPanel = ref(props.showSmartSelection)

// 计算属性
const isAllSelected = computed(() => {
  return detailsList.value.length > 0 &&
         selectedDetails.value.length === detailsList.value.length
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedDetails.value.length
  return selectedCount > 0 && selectedCount < detailsList.value.length
})

// 方法定义
const toggleSelectionPanel = () => {
  showSelectionPanel.value = !showSelectionPanel.value
}

const getRowClassName = ({ row }) => {
  const isSelected = selectedDetails.value.some(item => item.id === row.id)
  return isSelected ? 'selected-row' : ''
}

const handleView = (detail) => {
  try {
    emit('detail:view', { detail, timestamp: Date.now() })
  } catch (error) {
    console.error('查看详情失败:', error)
    emit('error', { type: 'VIEW_ERROR', detail, error })
  }
}

const handleEdit = (detail) => {
  try {
    emit('detail:edit', { detail, timestamp: Date.now() })
  } catch (error) {
    console.error('编辑详情失败:', error)
    emit('error', { type: 'EDIT_ERROR', detail, error })
  }
}

const handleDelete = (detail) => {
  try {
    emit('detail:delete', { detail, timestamp: Date.now() })
  } catch (error) {
    console.error('删除详情失败:', error)
    emit('error', { type: 'DELETE_ERROR', detail, error })
  }
}

const handleRowClick = (row) => {
  try {
    // 点击表格行触发查看详情
    emit('row:click', { row, timestamp: Date.now() })
  } catch (error) {
    console.error('行点击处理失败:', error)
    emit('error', { type: 'ROW_CLICK_ERROR', row, error })
  }
}

const handleSelectionChange = (selection) => {
  try {
    // 检查选择数量限制
    if (props.maxSelection > 0 && selection.length > props.maxSelection) {
      ElMessage.warning(`最多只能选择 ${props.maxSelection} 项`)
      // 恢复到之前的选择状态
      nextTick(() => {
        if (tableRef.value) {
          selectedDetails.value.forEach(item => {
            tableRef.value.toggleRowSelection(item, true)
          })
        }
      })
      return
    }

    ruleDetailsStore.setSelectedDetails(selection)
    emit('selection:change', {
      selection,
      count: selection.length,
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('选择变更处理失败:', error)
    emit('error', { type: 'SELECTION_ERROR', selection, error })
  }
}

const handleSelectAllChange = (checked) => {
  if (checked) {
    handleSelectAll()
  } else {
    handleSelectNone()
  }
}

const handleSelectionCommand = (command) => {
  switch (command) {
    case 'select-all':
      handleSelectAll()
      break
    case 'select-none':
      handleSelectNone()
      break
    case 'select-inverse':
      handleSelectInverse()
      break
    case 'select-visible':
      handleSelectVisible()
      break
    case 'select-active':
      handleSelectActive()
      break
  }
}

const handleSortChange = ({ prop, order }) => {
  try {
    emit('table:sort', {
      prop,
      order,
      fieldName: getFieldChineseName(prop) || prop,
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('排序处理失败:', error)
    emit('error', { type: 'SORT_ERROR', prop, order, error })
  }
}

const handleSizeChange = (size) => {
  try {
    const oldSize = pagination.value.pageSize
    pagination.value.pageSize = size
    pagination.value.page = 1

    emit('pagination:change', {
      type: 'size',
      pageSize: size,
      page: 1,
      oldSize,
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('分页大小变更失败:', error)
    emit('error', { type: 'PAGE_SIZE_ERROR', size, error })
  }
}

const handleCurrentChange = (page) => {
  try {
    const oldPage = pagination.value.page
    pagination.value.page = page

    emit('pagination:change', {
      type: 'page',
      page,
      pageSize: pagination.value.pageSize,
      oldPage,
      timestamp: Date.now()
    })
  } catch (error) {
    console.error('页码变更失败:', error)
    emit('error', { type: 'PAGE_CHANGE_ERROR', page, error })
  }
}

// 智能选择方法
const handleSelectAll = () => {
  if (props.maxSelection > 0 && detailsList.value.length > props.maxSelection) {
    ElMessage.warning(`最多只能选择 ${props.maxSelection} 项，当前页面有 ${detailsList.value.length} 项`)
    return
  }

  if (tableRef.value) {
    tableRef.value.clearSelection()
    detailsList.value.forEach(row => {
      tableRef.value.toggleRowSelection(row, true)
    })
  } else {
    // 卡片视图的全选
    ruleDetailsStore.setSelectedDetails([...detailsList.value])
    emit('selection-change', detailsList.value)
  }
}

const handleSelectNone = () => {
  if (tableRef.value) {
    tableRef.value.clearSelection()
  } else {
    ruleDetailsStore.setSelectedDetails([])
    emit('selection-change', [])
  }
}

const handleSelectInverse = () => {
  const currentSelected = selectedDetails.value
  const newSelection = detailsList.value.filter(item =>
    !currentSelected.some(selected => selected.id === item.id)
  )

  if (props.maxSelection > 0 && newSelection.length > props.maxSelection) {
    ElMessage.warning(`反选后将有 ${newSelection.length} 项，超过最大选择限制 ${props.maxSelection}`)
    return
  }

  if (tableRef.value) {
    tableRef.value.clearSelection()
    newSelection.forEach(row => {
      tableRef.value.toggleRowSelection(row, true)
    })
  } else {
    ruleDetailsStore.setSelectedDetails(newSelection)
    emit('selection-change', newSelection)
  }
}

const handleSelectVisible = () => {
  // 选择当前可见的项目（非删除状态）
  const visibleItems = detailsList.value.filter(item => item.status !== 'DELETED')

  if (props.maxSelection > 0 && visibleItems.length > props.maxSelection) {
    ElMessage.warning(`可见项目有 ${visibleItems.length} 项，超过最大选择限制 ${props.maxSelection}`)
    return
  }

  if (tableRef.value) {
    tableRef.value.clearSelection()
    visibleItems.forEach(row => {
      tableRef.value.toggleRowSelection(row, true)
    })
  } else {
    ruleDetailsStore.setSelectedDetails(visibleItems)
    emit('selection-change', visibleItems)
  }
}

const handleSelectActive = () => {
  // 选择激活状态的项目
  const activeItems = detailsList.value.filter(item => item.status === 'ACTIVE')

  if (props.maxSelection > 0 && activeItems.length > props.maxSelection) {
    ElMessage.warning(`激活项目有 ${activeItems.length} 项，超过最大选择限制 ${props.maxSelection}`)
    return
  }

  if (tableRef.value) {
    tableRef.value.clearSelection()
    activeItems.forEach(row => {
      tableRef.value.toggleRowSelection(row, true)
    })
  } else {
    ruleDetailsStore.setSelectedDetails(activeItems)
    emit('selection-change', activeItems)
  }
}

const handleSelectByCondition = (filteredItems, addToSelection = false) => {
  const newSelection = addToSelection
    ? [...selectedDetails.value, ...filteredItems.filter(item =>
        !selectedDetails.value.some(selected => selected.id === item.id)
      )]
    : filteredItems

  if (props.maxSelection > 0 && newSelection.length > props.maxSelection) {
    ElMessage.warning(`选择项目将有 ${newSelection.length} 项，超过最大选择限制 ${props.maxSelection}`)
    return
  }

  if (tableRef.value) {
    if (!addToSelection) {
      tableRef.value.clearSelection()
    }
    filteredItems.forEach(row => {
      tableRef.value.toggleRowSelection(row, true)
    })
  } else {
    ruleDetailsStore.setSelectedDetails(newSelection)
    emit('selection-change', newSelection)
  }
}

</script>

<style scoped>
.rule-details-table {
  margin-bottom: 20px;
}

.table-card {
  border: none;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.info-text {
  color: #909399;
  font-size: 14px;
}

.selection-info {
  font-size: 14px;
  color: #409eff;
  font-weight: 500;
}

.selection-count {
  font-weight: 600;
  color: #409eff;
}

.selection-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.rule-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rule-name {
  flex: 1;
}

.default-tag {
  flex-shrink: 0;
}

.error-level-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.level-tag {
  margin-top: 4px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.no-data {
  color: #c0c4cc;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 选中行样式 */
:deep(.selected-row) {
  background-color: #f0f9ff !important;
}

:deep(.selected-row:hover) {
  background-color: #e6f4ff !important;
}

/* 表格行点击样式 */
:deep(.el-table__row) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa !important;
}

/* 表格选择列样式优化 */
:deep(.el-table__header-wrapper .el-checkbox) {
  margin-right: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .table-info {
    justify-content: center;
  }

  .table-actions {
    justify-content: center;
  }
}
</style>
