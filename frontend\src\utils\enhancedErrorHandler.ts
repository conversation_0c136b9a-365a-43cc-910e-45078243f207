/**
 * 增强错误处理器
 * 实现分层错误处理机制，支持错误分类、用户友好提示和自动恢复
 */

// UI组件接口
interface UIMessage {
  (options: { message: string; type: string; duration?: number; showClose?: boolean }): void
}

interface UINotification {
  (options: { title: string; message: string; type: string; duration?: number; showClose?: boolean }): void
}

// 默认的控制台实现
const consoleMessage: UIMessage = (options) => {
  const message = typeof options === 'string' ? options : options.message
  if (options.type === 'error') {
    console.error('Message:', message)
  } else {
    console.warn('Message:', message)
  }
}

const consoleNotification: UINotification = (options) => {
  if (options.type === 'error') {
    console.error('Notification:', options.title, '-', options.message)
  } else {
    console.warn('Notification:', options.title, '-', options.message)
  }
}

// UI组件实例
let ElMessage: UIMessage = consoleMessage
let ElNotification: UINotification = consoleNotification

// 尝试初始化Element Plus组件（仅在浏览器环境）
if (typeof window !== 'undefined') {
  try {
    import('element-plus').then(elementPlus => {
      if (elementPlus.ElMessage && elementPlus.ElNotification) {
        // 包装Element Plus组件以匹配我们的接口
        ElMessage = (options) => {
          try {
            (elementPlus.ElMessage as any)(options)
          } catch (error) {
            consoleMessage(options)
          }
        }
        ElNotification = (options) => {
          try {
            (elementPlus.ElNotification as any)(options)
          } catch (error) {
            consoleNotification(options)
          }
        }
      }
    }).catch(() => {
      // 保持默认的控制台实现
    })
  } catch {
    // 保持默认的控制台实现
  }
}
import type { 
  EnhancedError, 
  ErrorCategory, 
  ErrorSeverity, 
  RecoveryStrategy 
} from '../types/apiEnhanced'
import { ErrorCategory as ErrorCategoryEnum, ErrorSeverity as ErrorSeverityEnum } from '../types/apiEnhanced'

/**
 * 增强错误处理器类
 */
export class EnhancedErrorHandler {
  private errorLog: EnhancedError[] = []
  private maxLogSize = 1000

  /**
   * 处理错误
   * @param error 原始错误
   * @param context 错误上下文
   * @returns 增强错误信息
   */
  handleError(error: any, context?: Record<string, any>): EnhancedError {
    const enhancedError = this.enhanceError(error, context)
    
    // 记录错误日志
    this.logError(enhancedError)
    
    // 显示用户提示
    this.showUserMessage(enhancedError)
    
    // 上报错误（如果需要）
    this.reportError(enhancedError)
    
    return enhancedError
  }

  /**
   * 增强错误信息
   * @param error 原始错误
   * @param context 错误上下文
   * @returns 增强错误信息
   */
  private enhanceError(error: any, context?: Record<string, any>): EnhancedError {
    const category = this.categorizeError(error)
    const severity = this.determineSeverity(error, category)
    const userMessage = this.generateUserMessage(error, category)
    
    return {
      category,
      severity,
      code: this.extractErrorCode(error),
      message: this.extractErrorMessage(error),
      userMessage,
      details: error.response?.data || error.details,
      timestamp: Date.now(),
      requestId: error.config?.headers?.['X-Request-ID'] || context?.requestId,
      context,
      stack: error.stack
    }
  }

  /**
   * 错误分类
   * @param error 错误对象
   * @returns 错误分类
   */
  categorizeError(error: any): ErrorCategory {
    // 网络错误
    if (!error.response && error.request) {
      return ErrorCategoryEnum.NETWORK
    }
    
    // HTTP状态码错误
    if (error.response) {
      const status = error.response.status
      
      if (status >= 400 && status < 500) {
        if (status === 401 || status === 403) {
          return ErrorCategoryEnum.PERMISSION
        }
        if (status === 422 || status === 400) {
          return ErrorCategoryEnum.VALIDATION
        }
        return ErrorCategoryEnum.BUSINESS
      }
      
      if (status >= 500) {
        return ErrorCategoryEnum.SYSTEM
      }
    }
    
    // 业务错误（基于错误码）
    const errorCode = this.extractErrorCode(error)
    if (typeof errorCode === 'string') {
      if (errorCode.startsWith('VALIDATION_')) {
        return ErrorCategoryEnum.VALIDATION
      }
      if (errorCode.startsWith('PERMISSION_')) {
        return ErrorCategoryEnum.PERMISSION
      }
      if (errorCode.startsWith('BUSINESS_')) {
        return ErrorCategoryEnum.BUSINESS
      }
    }
    
    return ErrorCategoryEnum.UNKNOWN
  }

  /**
   * 确定错误严重程度
   * @param error 错误对象
   * @param category 错误分类
   * @returns 错误严重程度
   */
  private determineSeverity(error: any, category: ErrorCategory): ErrorSeverity {
    // 网络错误通常是高严重程度
    if (category === ErrorCategoryEnum.NETWORK) {
      return ErrorSeverityEnum.HIGH
    }
    
    // 系统错误是关键严重程度
    if (category === ErrorCategoryEnum.SYSTEM) {
      return ErrorSeverityEnum.CRITICAL
    }
    
    // 权限错误是中等严重程度
    if (category === ErrorCategoryEnum.PERMISSION) {
      return ErrorSeverityEnum.MEDIUM
    }
    
    // 验证错误通常是低严重程度
    if (category === ErrorCategoryEnum.VALIDATION) {
      return ErrorSeverityEnum.LOW
    }
    
    // 业务错误根据具体情况判断
    if (category === ErrorCategoryEnum.BUSINESS) {
      const status = error.response?.status
      if (status === 404) {
        return ErrorSeverityEnum.MEDIUM
      }
      return ErrorSeverityEnum.LOW
    }
    
    return ErrorSeverityEnum.MEDIUM
  }

  /**
   * 生成用户友好的错误消息
   * @param error 错误对象
   * @param category 错误分类
   * @returns 用户友好消息
   */
  generateUserMessage(error: any, category: ErrorCategory): string {
    const status = error.response?.status
    // const errorCode = this.extractErrorCode(error) // 暂时未使用
    
    // 网络错误
    if (category === ErrorCategoryEnum.NETWORK) {
      return '网络连接失败，请检查网络设置后重试'
    }
    
    // 权限错误
    if (category === ErrorCategoryEnum.PERMISSION) {
      if (status === 401) {
        return '登录已过期，请重新登录'
      }
      if (status === 403) {
        return '权限不足，无法执行此操作'
      }
      return '权限验证失败'
    }
    
    // 验证错误
    if (category === ErrorCategoryEnum.VALIDATION) {
      const message = this.extractErrorMessage(error)
      if (message && message.includes('validation')) {
        return '数据验证失败，请检查输入内容'
      }
      return message || '输入数据格式不正确'
    }
    
    // 业务错误
    if (category === ErrorCategoryEnum.BUSINESS) {
      if (status === 404) {
        return '请求的资源不存在'
      }
      if (status === 409) {
        return '数据冲突，请刷新后重试'
      }
      
      // 尝试从响应中提取业务错误消息
      const businessMessage = error.response?.data?.message
      if (businessMessage) {
        return businessMessage
      }
    }
    
    // 系统错误
    if (category === ErrorCategoryEnum.SYSTEM) {
      return '系统暂时不可用，请稍后重试'
    }
    
    // 默认消息
    return this.extractErrorMessage(error) || '操作失败，请重试'
  }

  /**
   * 提取错误码
   * @param error 错误对象
   * @returns 错误码
   */
  private extractErrorCode(error: any): string | number {
    return error.response?.data?.code || 
           error.code || 
           error.response?.status || 
           'UNKNOWN_ERROR'
  }

  /**
   * 提取错误消息
   * @param error 错误对象
   * @returns 错误消息
   */
  private extractErrorMessage(error: any): string {
    return error.response?.data?.message || 
           error.message || 
           error.response?.statusText || 
           '未知错误'
  }

  /**
   * 显示用户消息
   * @param error 增强错误信息
   */
  private showUserMessage(error: EnhancedError): void {
    const { severity, userMessage, category } = error

    // 在测试环境中跳过UI显示
    if (typeof globalThis !== 'undefined' && (globalThis as any).process?.env?.NODE_ENV === 'test') {
      console.log(`[${severity.toUpperCase()}] ${userMessage}`)
      return
    }

    // 确保UI组件可用
    if (!ElMessage || !ElNotification) {
      console.warn('UI components not available, using console output:', userMessage)
      return
    }

    try {
      // 根据严重程度选择显示方式
      switch (severity) {
        case ErrorSeverityEnum.CRITICAL:
        case ErrorSeverityEnum.HIGH:
          if (ElNotification && typeof ElNotification === 'function') {
            ElNotification({
              title: '错误',
              message: userMessage,
              type: 'error',
              duration: 0, // 不自动关闭
              showClose: true
            })
          }
          break

        case ErrorSeverityEnum.MEDIUM:
          if (ElMessage && typeof ElMessage === 'function') {
            ElMessage({
              message: userMessage,
              type: 'error',
              duration: 5000,
              showClose: true
            })
          }
          break

        case ErrorSeverityEnum.LOW:
          if (category !== ErrorCategoryEnum.VALIDATION && ElMessage && typeof ElMessage === 'function') {
            ElMessage({
              message: userMessage,
              type: 'warning',
              duration: 3000
            })
          }
          break
      }
    } catch (error) {
      console.warn('Failed to show UI message, falling back to console:', userMessage)
    }
  }

  /**
   * 记录错误日志
   * @param error 增强错误信息
   */
  private logError(error: EnhancedError): void {
    // 添加到错误日志
    this.errorLog.unshift(error)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize)
    }
    
    // 控制台输出（开发环境）
    if ((import.meta as any).env?.DEV) {
      console.group(`🚨 ${error.category.toUpperCase()} Error`)
      console.error('Message:', error.message)
      console.error('User Message:', error.userMessage)
      console.error('Code:', error.code)
      console.error('Severity:', error.severity)
      if (error.context) {
        console.error('Context:', error.context)
      }
      if (error.details) {
        console.error('Details:', error.details)
      }
      if (error.stack) {
        console.error('Stack:', error.stack)
      }
      console.groupEnd()
    }
  }

  /**
   * 上报错误
   * @param error 增强错误信息
   */
  private reportError(error: EnhancedError): void {
    // 只上报高严重程度的错误
    if (error.severity === ErrorSeverityEnum.HIGH || 
        error.severity === ErrorSeverityEnum.CRITICAL) {
      
      // 这里可以集成错误监控服务，如 Sentry
      // 目前只是示例实现
      try {
        // 发送错误报告到监控服务
        // await errorReportingService.report(error)
        console.log('Error reported:', error.code)
      } catch (reportError) {
        console.warn('Failed to report error:', reportError)
      }
    }
  }

  /**
   * 获取错误恢复策略
   * @param error 增强错误信息
   * @returns 恢复策略
   */
  getRecoveryStrategy(error: EnhancedError): RecoveryStrategy {
    const { category, code } = error
    // const severity = error.severity // 暂时未使用
    
    // 网络错误 - 重试
    if (category === ErrorCategoryEnum.NETWORK) {
      return {
        canRecover: true,
        strategy: 'retry',
        maxRetries: 3,
        retryDelay: 1000
      }
    }
    
    // 权限错误 - 重定向到登录
    if (category === ErrorCategoryEnum.PERMISSION && code === 401) {
      return {
        canRecover: true,
        strategy: 'redirect',
        redirectUrl: '/login'
      }
    }
    
    // 系统错误 - 降级处理
    if (category === ErrorCategoryEnum.SYSTEM) {
      return {
        canRecover: true,
        strategy: 'fallback',
        fallbackData: null
      }
    }
    
    // 验证错误 - 忽略（由用户修正）
    if (category === ErrorCategoryEnum.VALIDATION) {
      return {
        canRecover: false,
        strategy: 'ignore'
      }
    }
    
    // 默认策略
    return {
      canRecover: false,
      strategy: 'ignore'
    }
  }

  /**
   * 获取错误日志
   * @param limit 限制数量
   * @returns 错误日志数组
   */
  getErrorLog(limit?: number): EnhancedError[] {
    return limit ? this.errorLog.slice(0, limit) : [...this.errorLog]
  }

  /**
   * 清除错误日志
   */
  clearErrorLog(): void {
    this.errorLog = []
  }

  /**
   * 获取错误统计
   * @returns 错误统计信息
   */
  getErrorStats(): Record<string, any> {
    const stats = {
      total: this.errorLog.length,
      byCategory: {} as Record<string, number>,
      bySeverity: {} as Record<string, number>,
      recent: this.errorLog.slice(0, 10)
    }
    
    for (const error of this.errorLog) {
      stats.byCategory[error.category] = (stats.byCategory[error.category] || 0) + 1
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1
    }
    
    return stats
  }
}

// 创建单例实例
export const enhancedErrorHandler = new EnhancedErrorHandler()

// 默认导出
export default enhancedErrorHandler
