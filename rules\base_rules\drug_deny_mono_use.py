from config.settings import settings
from models import PatientData, RuleOutput, RuleResult
from rules.base_rules.base import BaseRule


class DrugDenyMonoUseRule(BaseRule):
    """
    药品单独使用不予支付-规则
    """

    rule_key = "drug_deny_mono_use"
    rule_name = "药品单独使用不予支付"
    rule_desc = """1、排除自费患者、自费费用
2、精确匹配目标药品医保代码
3、匹配XB05BA开头的23位药品医保代码（胃肠外营养液）
4、记账时间为同一秒，有目标药品代码且没有XB05BA开头代码，提示"""

    def __init__(
        self,
        rule_id: str,                   # 规则ID
        yb_code: list[str],             # 药品编码
        drug_start_with: str,           # 搭配使用的药品编码前缀
        rule_name: str,                 # 规则名称
        level1: str,                    # 一级错误类型
        level2: str,                    # 二级错误类型
        level3: str,                    # 三级错误类型
        error_reason: str,              # 错误原因
        degree: str,                    # 错误程度
        reference: str,                 # 质控依据或参考资料
        detail_position: str,           # 具体位置描述
        prompted_fields3: str | None,   # 提示字段类型
        prompted_fields1: str,          # 提示字段编码
        type: str,                      # 规则类别
        pos: str,                       # 适用业务
        applicableArea: str,            # 适用地区
        default_use: str,               # 默认选用
        remarks: str | None,            # 备注信息
        in_illustration: str | None,    # 入参说明
        start_date: str,                # 开始日期
        end_date: str,                  # 结束日期
    ):
        super().__init__(rule_id)
        self.yb_code = yb_code
        self.drug_start_with = drug_start_with
        self.rule_name = rule_name
        self.level1 = level1
        self.level2 = level2
        self.level3 = level3
        self.type = type
        self.error_reason = error_reason
        self.degree = degree
        self.reference = reference
        self.prompted_fields3 = prompted_fields3
        self.prompted_fields1 = prompted_fields1
        self.detail_position = detail_position
        self.pos = pos
        self.applicableArea = applicableArea
        self.default_use = default_use
        self.remarks = remarks
        self.in_illustration = in_illustration
        self.start_date = start_date
        self.end_date = end_date

    def validate(self, patient_data: PatientData) -> RuleResult | None:
        """
        药品单独使用不予支付
        """
        # 1. 先判断患者的医保类型，如果是自费就返回，不违规
        if (
            not patient_data.patientMedicalInsuranceType
            or not isinstance(patient_data.patientMedicalInsuranceType, str)
            or "自费" in patient_data.patientMedicalInsuranceType
        ):
            return None

        # 疑似违规数据
        # 格式：{
        #     创建时间秒级时间戳: (
        #         使用量，
        #         使用id，
        #         使用日期集合，
        #         违规金额，
        #         违规量，
        #         违规id，
        #         违规日期集合，
        #         是否有搭配使用的药品
        #     )
        # }
        illegal_data = {}

        # 2. 遍历费用信息，拼接疑似违规数据
        for fee in patient_data.fees:
            bzjs = str(fee.bzjs)

            # 2.1 判断记账日期是否正确，至少是秒级时间戳，用于判断 使用天数和违规天数
            jzsj_str = str(fee.jzsj)
            if not jzsj_str.isdigit() or len(jzsj_str) < 10:
                continue
            jzsj_date_str = jzsj_str[:10]
            jzsj_date = self._trans_timestamp_to_date(int(jzsj_date_str))

            # 2.2 统计处方信息
            if jzsj_date_str not in illegal_data:
                illegal_data[jzsj_date_str] = [0, [], set(), 0, 0, [], set(), False]

            # 统计处方中的药品，为了加快统计速度，不统计不相关药品
            # 这里只统计两种：1. 违规药品；2. 必须搭配使用的药品（药品代码以 self.drug_start_with 开头）
            if fee.ybdm.startswith(self.drug_start_with):
                illegal_data[jzsj_date_str][7] = True

            # 但是实际统计数据，只包含违规药品，即 self.yb_codes
            if fee.ybdm in self.yb_code:
                # 无论是否自费，都要统计使用量、使用id、使用日期
                illegal_data[jzsj_date_str][0] += fee.sl
                illegal_data[jzsj_date_str][1].append(fee.id)
                illegal_data[jzsj_date_str][2].add(jzsj_date)

                # 只有医保支付的费用，才需要统计违规量
                if bzjs not in settings.FEE_SELF_PAY_CODE:
                    illegal_data[jzsj_date_str][3] += fee.je
                    illegal_data[jzsj_date_str][4] += fee.sl
                    illegal_data[jzsj_date_str][5].append(fee.id)
                    illegal_data[jzsj_date_str][6].add(jzsj_date)

        # 使用数量、违规数量
        used_count, illegal_count = 0, 0
        # 使用费用id列表、违规费用id列表
        used_fee_ids, illegal_fee_ids = [], []
        # 使用日期集合、违规日期集合
        used_dates, illegal_dates = set(), set()
        # 违规金额
        error_fee = 0

        # 3. 遍历疑似违规数据，判断：
        #    单个处方内是否有搭配使用的药品
        #      如果没有搭配使用的药品，则为违规
        for _, fee_info in illegal_data.items():
            # 3.1 判断费用代码集合是否是指定集合的子集
            if not fee_info[7]:
                # 3.2 统计使用数量、使用费用id列表、使用日期集合
                used_count += fee_info[0]
                used_fee_ids.extend(fee_info[1])
                used_dates.update(fee_info[2])
                # 3.3 统计违规数量、违规费用id列表、违规日期集合、违规金额
                error_fee += fee_info[3]
                illegal_count += fee_info[4]
                illegal_fee_ids.extend(fee_info[5])
                illegal_dates.update(fee_info[6])

        # 4. 如果违规数量为0，则返回，不违规
        if illegal_count == 0:
            return None

        rule_output = RuleOutput(
            type_=self.type,
            message=self.error_reason,
            level1=self.level1,
            level2=self.level2,
            level3=self.level3,
            error_reason=self.error_reason,
            degree=self.degree,
            reference=self.reference,
            prompted_fields3=self.prompted_fields3,
            prompted_fields1=self.prompted_fields1,
            detail_position=self.detail_position,
            pos=self.pos,
            applicableArea=self.applicableArea,
            default_use=self.default_use,
            remarks=self.remarks,
            in_illustration=self.in_illustration,
            start_date=self.start_date,
            end_date=self.end_date,
            used_count=used_count,
            illegal_count=illegal_count,
            prompted_fields2=",".join(used_fee_ids),
            illegal_item=",".join(illegal_fee_ids),
            used_day=len(used_dates),
            illegal_day=len(illegal_dates),
            error_fee=error_fee,
        )

        # 返回结果
        return RuleResult(
            id=self.rule_id,
            output=rule_output,
        )
