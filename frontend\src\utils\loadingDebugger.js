/**
 * 加载状态调试工具
 * 用于开发时检测和调试加载状态问题
 */

/**
 * 全局加载状态调试器
 */
export class LoadingDebugger {
  constructor() {
    this.isEnabled = process.env.NODE_ENV === 'development'
    this.taskHistory = []
    this.maxHistorySize = 100
    
    if (this.isEnabled) {
      this.setupGlobalDebugger()
    }
  }

  /**
   * 设置全局调试器
   */
  setupGlobalDebugger() {
    // 将调试器挂载到 window 对象
    window.__LOADING_DEBUGGER__ = this
    
    // 定期检查泄漏
    setInterval(() => {
      this.checkForLeaks()
    }, 10000) // 每10秒检查一次
  }

  /**
   * 记录任务操作
   */
  logTask(action, taskId, message = '') {
    if (!this.isEnabled) return

    const record = {
      timestamp: Date.now(),
      action, // 'ADD' | 'REMOVE'
      taskId,
      message,
      stack: new Error().stack
    }

    this.taskHistory.push(record)
    
    // 限制历史记录大小
    if (this.taskHistory.length > this.maxHistorySize) {
      this.taskHistory.shift()
    }

    console.debug(`[LoadingDebugger] ${action}: ${taskId}`, record)
  }

  /**
   * 检查任务泄漏
   */
  checkForLeaks() {
    if (!this.isEnabled) return

    try {
      // 尝试获取 appStore
      const rootEl = document.querySelector('#app')
      if (!rootEl?.__vue_app__) return

      const app = rootEl.__vue_app__
      const pinia = app.config.globalProperties.$pinia
      if (!pinia) return

      const appStore = pinia._s.get('app')
      if (!appStore) return

      const activeTasks = appStore.loadingTasks
      if (activeTasks && activeTasks.size > 0) {
        console.warn('[LoadingDebugger] 检测到可能的任务泄漏:', {
          activeTasks: Array.from(activeTasks),
          taskCount: activeTasks.size,
          recentHistory: this.getRecentHistory(10)
        })
      }
    } catch (error) {
      console.debug('[LoadingDebugger] 检查泄漏时出错:', error)
    }
  }

  /**
   * 获取最近的任务历史
   */
  getRecentHistory(count = 20) {
    return this.taskHistory.slice(-count)
  }

  /**
   * 分析任务配对情况
   */
  analyzeTaskPairing() {
    const taskMap = new Map()
    const unpaired = []

    this.taskHistory.forEach(record => {
      const { action, taskId } = record
      
      if (action === 'ADD') {
        if (taskMap.has(taskId)) {
          unpaired.push({
            type: 'DUPLICATE_ADD',
            taskId,
            records: [taskMap.get(taskId), record]
          })
        } else {
          taskMap.set(taskId, record)
        }
      } else if (action === 'REMOVE') {
        if (taskMap.has(taskId)) {
          taskMap.delete(taskId)
        } else {
          unpaired.push({
            type: 'ORPHAN_REMOVE',
            taskId,
            record
          })
        }
      }
    })

    // 剩余的都是未配对的 ADD
    taskMap.forEach((record, taskId) => {
      unpaired.push({
        type: 'UNPAIRED_ADD',
        taskId,
        record
      })
    })

    return {
      unpaired,
      summary: {
        total: this.taskHistory.length,
        unpairedCount: unpaired.length,
        duplicateAdds: unpaired.filter(u => u.type === 'DUPLICATE_ADD').length,
        orphanRemoves: unpaired.filter(u => u.type === 'ORPHAN_REMOVE').length,
        unpairedAdds: unpaired.filter(u => u.type === 'UNPAIRED_ADD').length
      }
    }
  }

  /**
   * 生成调试报告
   */
  generateReport() {
    const analysis = this.analyzeTaskPairing()
    
    return {
      enabled: this.isEnabled,
      timestamp: Date.now(),
      taskHistory: this.taskHistory,
      analysis,
      currentState: this.getCurrentState()
    }
  }

  /**
   * 获取当前状态
   */
  getCurrentState() {
    try {
      const rootEl = document.querySelector('#app')
      if (!rootEl?.__vue_app__) return null

      const app = rootEl.__vue_app__
      const pinia = app.config.globalProperties.$pinia
      if (!pinia) return null

      const appStore = pinia._s.get('app')
      if (!appStore) return null

      return {
        globalLoading: appStore.globalLoading,
        loadingTasks: Array.from(appStore.loadingTasks || []),
        taskCount: appStore.loadingTasks?.size || 0
      }
    } catch (error) {
      return { error: error.message }
    }
  }

  /**
   * 清理所有泄漏的任务
   */
  cleanupLeaks() {
    try {
      const rootEl = document.querySelector('#app')
      if (!rootEl?.__vue_app__) return false

      const app = rootEl.__vue_app__
      const pinia = app.config.globalProperties.$pinia
      if (!pinia) return false

      const appStore = pinia._s.get('app')
      if (!appStore) return false

      const beforeCount = appStore.loadingTasks?.size || 0
      appStore.clearLoadingTasks()
      const afterCount = appStore.loadingTasks?.size || 0

      console.log(`[LoadingDebugger] 清理完成: ${beforeCount} -> ${afterCount}`)
      return true
    } catch (error) {
      console.error('[LoadingDebugger] 清理失败:', error)
      return false
    }
  }
}

// 创建全局实例
export const loadingDebugger = new LoadingDebugger()

// 开发时的便捷方法
if (process.env.NODE_ENV === 'development') {
  window.debugLoading = {
    report: () => loadingDebugger.generateReport(),
    cleanup: () => loadingDebugger.cleanupLeaks(),
    check: () => loadingDebugger.checkForLeaks(),
    history: (count) => loadingDebugger.getRecentHistory(count)
  }
}
