/**
 * API增强类型定义
 * 支持字段映射引擎、缓存策略和错误处理的类型定义
 */

// ==================== 字段映射相关类型 ====================

/**
 * 字段定义接口
 */
export interface FieldDefinition {
  chinese_name: string
  data_type: 'string' | 'text' | 'integer' | 'array' | 'boolean'
  required: boolean
  max_length?: number
  min_value?: number
  max_value?: number
  description: string
  database_column: string
  api_field: string
  excel_column: string
  validation_rules?: string[]
  rule_types?: string[]
}

/**
 * 字段映射配置接口
 */
export interface FieldMappingConfig {
  metadata: {
    version: string
    last_updated: string
    description: string
    author: string
    tables: string[]
    changelog?: string
  }
  field_definitions: {
    common_fields: Record<string, FieldDefinition>
    specific_fields?: Record<string, FieldDefinition>
  }
  rule_type_mappings?: Record<string, {
    name: string
    required_fields: string[]
    optional_fields: string[]
  }>
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

// ==================== API缓存相关类型 ====================

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  ttl: number                    // 缓存生存时间（毫秒）
  maxSize: number               // 最大缓存条目数
  enablePersistence: boolean    // 是否启用持久化
  versionBased: boolean         // 是否基于版本缓存
  keyPrefix: string             // 缓存键前缀
}

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  version?: string
  ttl: number
  accessCount: number
  lastAccessed: number
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  totalItems: number
  hitCount: number
  missCount: number
  hitRate: number
  memoryUsage: number
  oldestItem?: number
  newestItem?: number
}

// ==================== 错误处理相关类型 ====================

/**
 * 错误分类枚举
 */
export enum ErrorCategory {
  NETWORK = 'network',
  VALIDATION = 'validation',
  BUSINESS = 'business',
  PERMISSION = 'permission',
  SYSTEM = 'system',
  UNKNOWN = 'unknown'
}

/**
 * 错误严重程度枚举
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * 增强错误信息接口
 */
export interface EnhancedError {
  category: ErrorCategory
  severity: ErrorSeverity
  code: string | number
  message: string
  userMessage: string
  details?: any
  timestamp: number
  requestId?: string
  context?: Record<string, any>
  stack?: string
}

/**
 * 错误恢复策略接口
 */
export interface RecoveryStrategy {
  canRecover: boolean
  strategy: 'retry' | 'fallback' | 'ignore' | 'redirect'
  maxRetries?: number
  retryDelay?: number
  fallbackData?: any
  redirectUrl?: string
}

// ==================== API请求/响应增强类型 ====================

/**
 * 增强的API请求配置
 */
export interface EnhancedRequestConfig {
  url: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  
  // 增强功能配置
  enableCache?: boolean
  cacheKey?: string
  cacheTtl?: number
  enableFieldMapping?: boolean
  enableRetry?: boolean
  maxRetries?: number
  retryDelay?: number
  timeout?: number
  
  // 错误处理配置
  showErrorMessage?: boolean
  customErrorHandler?: (error: EnhancedError) => void
  
  // 数据转换配置
  transformRequest?: (data: any) => any
  transformResponse?: (data: any) => any
  
  // 验证配置
  validateRequest?: boolean
  validateResponse?: boolean
  ruleKey?: string
}

/**
 * 增强的API响应接口
 */
export interface EnhancedApiResponse<T = any> {
  success: boolean
  code: number
  message: string
  data: T
  timestamp?: number
  requestId?: string
  
  // 缓存相关
  fromCache?: boolean
  cacheKey?: string
  cacheTimestamp?: number
  
  // 性能相关
  responseTime?: number
  
  // 调试信息
  debug?: {
    originalData?: any
    transformedData?: any
    validationResult?: ValidationResult
  }
}

// ==================== 批量操作相关类型 ====================

/**
 * 批量操作请求接口
 */
export interface BatchOperationRequest<T = any> {
  operations: Array<{
    action: 'CREATE' | 'UPDATE' | 'DELETE'
    id?: string | number
    data?: T
  }>
  options?: {
    continueOnError?: boolean
    validateAll?: boolean
    enableTransaction?: boolean
  }
}

/**
 * 批量操作响应接口
 */
export interface BatchOperationResponse {
  success: boolean
  totalOperations: number
  successfulOperations: number
  failedOperations: number
  results: Array<{
    index: number
    success: boolean
    data?: any
    error?: string
  }>
  summary: {
    created: number
    updated: number
    deleted: number
    errors: number
  }
}

// ==================== 分页相关类型 ====================

/**
 * 分页请求参数
 */
export interface PaginationParams {
  page: number
  page_size: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

/**
 * 分页响应数据
 */
export interface PaginationResponse<T = any> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
  has_next: boolean
  has_prev: boolean
}

// ==================== 搜索和过滤相关类型 ====================

/**
 * 搜索参数接口
 */
export interface SearchParams {
  keyword?: string
  fields?: string[]
  filters?: Record<string, any>
  dateRange?: {
    start: string
    end: string
    field: string
  }
}

/**
 * 过滤条件接口
 */
export interface FilterCondition {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'like' | 'between'
  value: any
  values?: any[]
}

// ==================== 统计和分析相关类型 ====================

/**
 * 统计数据接口
 */
export interface StatisticsData {
  total_count: number
  status_distribution: Record<string, number>
  type_distribution: Record<string, number>
  date_distribution: Record<string, number>
  trend_data?: Array<{
    date: string
    count: number
  }>
}

/**
 * 性能监控数据接口
 */
export interface PerformanceMetrics {
  apiCalls: number
  averageResponseTime: number
  errorRate: number
  cacheHitRate: number
  slowQueries: Array<{
    url: string
    responseTime: number
    timestamp: number
  }>
}

// ==================== 导出类型 ====================

// 所有类型和枚举已在上面使用export关键字导出，无需重复导出
