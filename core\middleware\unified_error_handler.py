"""
统一错误处理中间件
主从节点通用的错误处理机制，确保响应格式一致性和系统稳定性
"""

import traceback
from collections.abc import Callable
from typing import Any

from fastapi import HTTPException, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from core.constants.error_codes import <PERSON>rrorCodeHelper, ErrorCodes
from core.constants.error_messages import ErrorMessages, MessageFormatter
from core.logging.logging_system import log as logger
from core.middleware.request_tracking import request_tracker
from models.api import ApiResponse


class UnifiedErrorHandlingMiddleware:
    """
    统一错误处理中间件
    提供主从节点通用的错误处理机制
    """

    @staticmethod
    def create_error_response(
        code: int,
        message: str,
        data: Any = None,
        request_id: str | None = None,
        debug_info: dict[str, Any] | None = None,
        include_debug: bool = False,
    ) -> JSONResponse:
        """
        创建标准错误响应

        Args:
            code: 错误状态码
            message: 错误消息
            data: 错误相关数据
            request_id: 请求追踪ID
            debug_info: 调试信息
            include_debug: 是否包含调试信息

        Returns:
            JSONResponse: 标准化的错误响应
        """
        # 创建响应对象
        response_data = ApiResponse.error_response(
            code=code,
            message=message,
            data=data,
            request_id=request_id,
            debug_info=debug_info if include_debug else None,
        )

        # 序列化响应
        content = response_data.model_dump(exclude_none=False)

        # 在生产环境中移除调试信息
        if not include_debug and "debug_info" in content:
            del content["debug_info"]

        return JSONResponse(
            status_code=status.HTTP_200_OK,  # 统一使用200状态码
            content=content,
        )

    @staticmethod
    async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
        """
        处理请求验证错误

        将Pydantic验证错误转换为用户友好的错误消息
        支持详细的错误定位和批量错误处理
        """
        request_id = getattr(request.state, "request_id", None)

        try:
            # 尝试获取请求体用于日志记录
            try:
                body = await request.json()
            except Exception:
                body = None

            # 解析验证错误
            field_errors = {}
            for error in exc.errors():
                loc = error.get("loc", [])
                msg = error.get("msg", "未知错误")
                error_type = error.get("type", "")

                # 构建字段路径
                if len(loc) > 1:
                    # 跳过第一个元素（通常是'body'）
                    field_path = ".".join(str(loc_part) for loc_part in loc[1:])

                    # 根据错误类型生成友好的错误消息
                    if "missing" in error_type:
                        field_errors[field_path] = "缺少必填字段"
                    elif "type_error" in error_type:
                        field_errors[field_path] = f"字段类型错误: {msg}"
                    elif "value_error" in error_type:
                        field_errors[field_path] = f"字段值错误: {msg}"
                    else:
                        field_errors[field_path] = msg
                else:
                    field_errors["request"] = f"请求错误: {msg}"

            # 格式化错误消息
            user_message = MessageFormatter.format_validation_error(field_errors)

            # 记录详细的错误日志
            logger.warning(
                f"Request validation failed: {request_id}",
                extra={
                    "request_id": request_id,
                    "endpoint": request.url.path,
                    "method": request.method,
                    "field_errors": field_errors,
                    "request_body": body,
                    "raw_errors": exc.errors(),
                },
            )

            # 添加请求追踪事件
            if request_id:
                request_tracker.add_event(
                    request_id, "validation_error", {"field_errors": field_errors, "error_count": len(field_errors)}
                )

            # 创建调试信息
            debug_info = (
                {"field_errors": field_errors, "raw_errors": exc.errors(), "request_body": body}
                if body is not None
                else {"field_errors": field_errors}
            )

            return UnifiedErrorHandlingMiddleware.create_error_response(
                code=ErrorCodes.VALIDATION_ERROR,
                message=user_message,
                request_id=request_id,
                debug_info=debug_info,
                include_debug=False,  # 生产环境不包含调试信息
            )

        except Exception as e:
            # 如果处理验证错误时发生异常，记录并返回通用错误
            logger.error(f"Error handling validation exception: {str(e)}", exc_info=True)
            return UnifiedErrorHandlingMiddleware.create_error_response(
                code=ErrorCodes.INTERNAL_ERROR,
                message=ErrorMessages.get_message(ErrorCodes.INTERNAL_ERROR),
                request_id=request_id,
            )

    @staticmethod
    async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
        """
        处理HTTP异常

        将FastAPI的HTTPException转换为统一的响应格式
        特别处理404 Not Found错误，确保不泄露路径信息
        """
        request_id = getattr(request.state, "request_id", None)

        # 特殊处理404错误
        if exc.status_code == 404:
            return await UnifiedErrorHandlingMiddleware.not_found_handler(request, exc)

        # 映射HTTP状态码到业务错误码
        error_code = exc.status_code
        if error_code not in [code.value for code in ErrorCodes]:
            # 如果不是预定义的错误码，使用通用错误码
            if 400 <= error_code < 500:
                error_code = ErrorCodes.BAD_REQUEST
            elif 500 <= error_code < 600:
                error_code = ErrorCodes.INTERNAL_ERROR
            else:
                error_code = ErrorCodes.INTERNAL_ERROR

        # 获取用户友好的错误消息
        user_message = ErrorMessages.get_message(error_code)
        if exc.detail and exc.detail != user_message:
            # 如果有具体的错误详情，添加到消息中
            user_message = ErrorMessages.get_detailed_message(error_code, exc.detail)

        # 记录错误日志
        logger.warning(
            f"HTTP exception: {request_id} - {exc.status_code}",
            extra={
                "request_id": request_id,
                "endpoint": request.url.path,
                "method": request.method,
                "status_code": exc.status_code,
                "detail": exc.detail,
            },
        )

        # 添加请求追踪事件
        if request_id:
            request_tracker.add_event(
                request_id, "http_exception", {"status_code": exc.status_code, "detail": exc.detail}
            )

        return UnifiedErrorHandlingMiddleware.create_error_response(
            code=error_code,
            message=user_message,
            request_id=request_id,
            debug_info={"original_status": exc.status_code, "original_detail": exc.detail},
        )

    @staticmethod
    async def not_found_handler(request: Request, exc: HTTPException = None) -> JSONResponse:
        """
        专门处理404 Not Found错误

        确保不泄露路径信息，提供用户友好的错误消息
        """
        request_id = getattr(request.state, "request_id", None)

        # 记录404错误，但不在日志中暴露完整路径
        logger.warning(
            f"404 Not Found: {request_id} - {request.method}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "status_code": 404,
                "error_type": "not_found",
                # 不记录完整路径，只记录路径长度用于分析
                "path_length": len(request.url.path),
                "has_query": bool(request.url.query),
            },
        )

        # 添加请求追踪事件
        if request_id:
            request_tracker.add_event(
                request_id,
                "not_found_error",
                {"status_code": 404, "method": request.method, "path_length": len(request.url.path)},
            )
            request_tracker.complete_request(request_id, success=False)

        # 返回用户友好的404错误，不泄露路径信息
        return UnifiedErrorHandlingMiddleware.create_error_response(
            code=ErrorCodes.NOT_FOUND,
            message="请求的接口不存在",
            data=None,  # 明确设置data字段
            request_id=request_id,
            debug_info={"error_type": "not_found", "method": request.method},
            include_debug=False,  # 生产环境不包含调试信息
        )

    @staticmethod
    async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """
        处理通用异常 - 系统的最后一道防线

        确保系统在任何情况下都不会崩溃
        记录详细的错误信息用于调试，但不向用户暴露内部实现细节
        """
        request_id = getattr(request.state, "request_id", None)

        try:
            # 确定错误类型和严重程度
            error_category = "unknown"
            error_code = ErrorCodes.INTERNAL_ERROR

            # 根据异常类型确定错误码
            if isinstance(exc, ValidationError):
                error_code = ErrorCodes.DATA_VALIDATION_ERROR
                error_category = "validation"
            elif isinstance(exc, ConnectionError):
                error_code = ErrorCodes.NETWORK_ERROR
                error_category = "network"
            elif isinstance(exc, MemoryError):
                error_code = ErrorCodes.MEMORY_ERROR
                error_category = "system"
            elif isinstance(exc, FileNotFoundError):
                error_code = ErrorCodes.FILE_SYSTEM_ERROR
                error_category = "filesystem"
            elif "database" in str(exc).lower() or "sql" in str(exc).lower():
                error_code = ErrorCodes.DATABASE_ERROR
                error_category = "database"
            elif "cache" in str(exc).lower() or "redis" in str(exc).lower():
                error_code = ErrorCodes.CACHE_ERROR
                error_category = "cache"

            # 获取用户友好的错误消息
            user_message = ErrorMessages.get_message(error_code)

            # 记录详细的错误日志
            logger.error(
                f"Unhandled exception: {request_id} - {type(exc).__name__}",
                extra={
                    "request_id": request_id,
                    "endpoint": request.url.path,
                    "method": request.method,
                    "exception_type": type(exc).__name__,
                    "exception_message": str(exc),
                    "error_category": error_category,
                    "stack_trace": traceback.format_exc(),
                },
                exc_info=True,
            )

            # 添加请求追踪事件
            if request_id:
                request_tracker.add_event(
                    request_id,
                    "general_exception",
                    {
                        "exception_type": type(exc).__name__,
                        "error_category": error_category,
                        "severity": ErrorCodeHelper.get_error_severity(error_code),
                    },
                )
                # 标记请求失败
                request_tracker.complete_request(request_id, success=False)

            # 创建调试信息（仅在开发环境使用）
            debug_info = {
                "exception_type": type(exc).__name__,
                "exception_message": str(exc),
                "error_category": error_category,
                "stack_trace": traceback.format_exc(),
            }

            return UnifiedErrorHandlingMiddleware.create_error_response(
                code=error_code,
                message=user_message,
                request_id=request_id,
                debug_info=debug_info,
                include_debug=False,  # 生产环境不包含调试信息
            )

        except Exception as handler_error:
            # 如果错误处理器本身出错，使用最基本的错误响应
            logger.critical(
                f"Error in exception handler: {str(handler_error)}",
                extra={
                    "request_id": request_id,
                    "original_exception": str(exc),
                    "handler_exception": str(handler_error),
                },
                exc_info=True,
            )

            # 返回最基本的错误响应，确保不会再次出错
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content={
                    "code": ErrorCodes.INTERNAL_ERROR,
                    "success": False,
                    "message": "系统内部错误，请联系管理员",
                    "data": None,
                    "request_id": request_id,
                    "timestamp": None,
                },
            )

    @staticmethod
    def get_handlers() -> dict[type[Exception], Callable]:
        """
        获取所有异常处理器

        Returns:
            Dict: 异常类型到处理器的映射
        """
        return {
            RequestValidationError: UnifiedErrorHandlingMiddleware.validation_exception_handler,
            HTTPException: UnifiedErrorHandlingMiddleware.http_exception_handler,
            StarletteHTTPException: UnifiedErrorHandlingMiddleware.http_exception_handler,
            Exception: UnifiedErrorHandlingMiddleware.general_exception_handler,
        }

    @staticmethod
    def register_handlers(app):
        """
        注册所有异常处理器到FastAPI应用

        Args:
            app: FastAPI应用实例
        """
        handlers = UnifiedErrorHandlingMiddleware.get_handlers()
        for exception_type, handler in handlers.items():
            app.add_exception_handler(exception_type, handler)

        # 专门注册404处理器
        app.add_exception_handler(404, UnifiedErrorHandlingMiddleware.not_found_handler)

        logger.info("Unified error handlers registered successfully")
