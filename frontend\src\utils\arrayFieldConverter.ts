/**
 * 数组字段转换工具
 * 用于处理前端Excel上传时逗号分隔字符串转换为数组的逻辑
 */

/**
 * 数组字段列表
 * 这些字段在后端期望接收数组格式，前端需要进行转换
 */
export const ARRAY_FIELDS = [
  'yb_code',
  'diag_whole_code', 
  'diag_code_prefix',
  'fee_whole_code',
  'fee_code_prefix'
] as const

export type ArrayFieldName = typeof ARRAY_FIELDS[number]

/**
 * 检查字段是否为数组字段
 * @param fieldName 字段名
 * @returns 是否为数组字段
 */
export function isArrayField(fieldName: string): fieldName is ArrayFieldName {
  return ARRAY_FIELDS.includes(fieldName as ArrayFieldName)
}

/**
 * 将逗号分隔的字符串转换为数组
 * @param value 输入值
 * @returns 转换后的数组
 */
export function convertStringToArray(value: any): string[] {
  if (value === null || value === undefined || value === '') {
    return []
  }

  // 如果已经是数组，直接返回
  if (Array.isArray(value)) {
    return value.map(item => String(item).trim()).filter(Boolean)
  }

  // 转换为字符串并处理
  const stringValue = String(value)
  
  // 移除方括号和引号
  const cleanValue = stringValue
    .replace(/^\[|\]$/g, '') // 移除首尾的方括号
    .replace(/["']/g, '') // 移除引号
  
  // 按逗号、分号或中文逗号分割
  return cleanValue
    .split(/[,;，；]/)
    .map(item => item.trim())
    .filter(Boolean) // 移除空字符串
}

/**
 * 将数组转换为逗号分隔的字符串（用于显示）
 * @param value 输入值
 * @returns 转换后的字符串
 */
export function convertArrayToString(value: any): string {
  if (value === null || value === undefined) {
    return ''
  }

  if (Array.isArray(value)) {
    return value.join(', ')
  }

  return String(value)
}

/**
 * 转换表单数据中的数组字段
 * @param formData 表单数据
 * @returns 转换后的表单数据
 */
export function convertFormArrayFields(formData: Record<string, any>): Record<string, any> {
  const converted = { ...formData }

  for (const [key, value] of Object.entries(converted)) {
    if (isArrayField(key)) {
      converted[key] = convertStringToArray(value)
    }
  }

  return converted
}

/**
 * 转换Excel行数据中的数组字段
 * @param rowData Excel行数据
 * @returns 转换后的行数据
 */
export function convertExcelRowArrayFields(rowData: Record<string, any>): Record<string, any> {
  const converted = { ...rowData }

  for (const [key, value] of Object.entries(converted)) {
    if (isArrayField(key)) {
      converted[key] = convertStringToArray(value)
    }
  }

  return converted
}

/**
 * 验证数组字段格式
 * @param fieldName 字段名
 * @param value 字段值
 * @returns 验证结果
 */
export function validateArrayField(fieldName: string, value: any): {
  valid: boolean
  error?: string
  converted?: string[]
} {
  if (!isArrayField(fieldName)) {
    return { valid: true }
  }

  try {
    const converted = convertStringToArray(value)
    
    // 检查是否为空（如果字段是必填的）
    if (converted.length === 0 && value !== null && value !== undefined && value !== '') {
      return {
        valid: false,
        error: `${fieldName} 字段格式错误，无法解析为有效的数组`
      }
    }

    return {
      valid: true,
      converted
    }
  } catch (error) {
    return {
      valid: false,
      error: `${fieldName} 字段转换失败: ${error instanceof Error ? error.message : '未知错误'}`
    }
  }
}

/**
 * 批量转换数据中的数组字段
 * @param dataList 数据列表
 * @returns 转换后的数据列表
 */
export function batchConvertArrayFields(dataList: Record<string, any>[]): Record<string, any>[] {
  return dataList.map(item => convertFormArrayFields(item))
}

/**
 * 获取数组字段的显示文本
 * @param fieldName 字段名
 * @param value 字段值
 * @returns 显示文本
 */
export function getArrayFieldDisplayText(fieldName: string, value: any): string {
  if (!isArrayField(fieldName)) {
    return String(value || '')
  }

  const converted = convertStringToArray(value)
  return converted.join(', ')
}

/**
 * 检查数组字段是否为空
 * @param value 字段值
 * @returns 是否为空
 */
export function isArrayFieldEmpty(value: any): boolean {
  const converted = convertStringToArray(value)
  return converted.length === 0
}

/**
 * 合并数组字段值
 * @param values 多个值
 * @returns 合并后的数组
 */
export function mergeArrayFieldValues(...values: any[]): string[] {
  const allItems: string[] = []
  
  for (const value of values) {
    const converted = convertStringToArray(value)
    allItems.push(...converted)
  }
  
  // 去重并排序
  return Array.from(new Set(allItems)).sort()
}

/**
 * 数组字段转换配置
 */
export const ARRAY_FIELD_CONFIG = {
  yb_code: {
    displayName: '药品编码',
    required: true,
    description: '药品编码列表，支持多个编码'
  },
  diag_whole_code: {
    displayName: '完整诊断编码',
    required: false,
    description: '完整诊断编码列表'
  },
  diag_code_prefix: {
    displayName: '诊断编码前缀',
    required: false,
    description: '诊断编码前缀列表'
  },
  fee_whole_code: {
    displayName: '费用完整编码',
    required: false,
    description: '费用完整编码列表'
  },
  fee_code_prefix: {
    displayName: '费用编码前缀',
    required: false,
    description: '费用编码前缀列表'
  }
} as const

/**
 * 获取数组字段配置
 * @param fieldName 字段名
 * @returns 字段配置
 */
export function getArrayFieldConfig(fieldName: string) {
  return ARRAY_FIELD_CONFIG[fieldName as ArrayFieldName]
}
