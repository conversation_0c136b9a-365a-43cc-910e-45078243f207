<template>
  <div class="submission-confirm">
    <div class="confirm-content">
      <!-- 确认信息 -->
      <el-alert
        :title="confirmTitle"
        :description="confirmDescription"
        type="warning"
        show-icon
        :closable="false"
        class="confirm-alert"
      />

      <!-- 数据统计 -->
      <div class="data-statistics">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="stat-card">
              <div class="stat-icon valid">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ validCount }}</div>
                <div class="stat-label">有效数据</div>
              </div>
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="stat-card">
              <div class="stat-icon invalid">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ invalidCount }}</div>
                <div class="stat-label">无效数据</div>
              </div>
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="stat-card">
              <div class="stat-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ totalCount }}</div>
                <div class="stat-label">总数据量</div>
              </div>
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="stat-card">
              <div class="stat-icon rule">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-text">{{ ruleName || ruleKey }}</div>
                <div class="stat-label">目标规则</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 上传模式说明 -->
      <div class="mode-info">
        <el-card shadow="never" class="mode-card">
          <template #header>
            <div class="mode-header">
              <el-icon><Upload /></el-icon>
              <span>{{ uploadMode === 'full' ? '全量上传模式' : '增量上传模式' }}</span>
            </div>
          </template>

          <div v-if="uploadMode === 'full'" class="mode-description">
            <p>将完全替换现有的规则数据，包括：</p>
            <ul>
              <li>删除所有现有数据</li>
              <li>导入 {{ validCount }} 条新数据</li>
              <li>重新建立数据索引</li>
            </ul>
          </div>

          <div v-else class="mode-description">
            <p>将执行增量数据更新，包括：</p>
            <ul>
              <li>新增 {{ operationSummary?.create || 0 }} 条数据</li>
              <li>更新 {{ operationSummary?.update || 0 }} 条数据</li>
              <li>删除 {{ operationSummary?.delete || 0 }} 条数据</li>
            </ul>
          </div>
        </el-card>
      </div>

      <!-- 注意事项 -->
      <div class="notice-section">
        <el-alert
          title="重要提醒"
          type="error"
          :closable="false"
          show-icon
        >
          <ul class="notice-list">
            <li>数据提交后将立即生效，请确认数据的准确性</li>
            <li>{{ uploadMode === 'full' ? '全量上传将完全替换现有数据，无法撤销' : '增量更新操作无法撤销，请仔细确认变更内容' }}</li>
            <li>建议在非业务高峰期进行数据更新操作</li>
            <li v-if="invalidCount > 0">请先处理所有无效数据后再提交</li>
          </ul>
        </el-alert>
      </div>

      <!-- 操作按钮 -->
      <div class="confirm-actions">
        <el-button @click="handlePrevStep" size="large">
          <el-icon><ArrowLeft /></el-icon>
          上一步
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="submitting"
          :disabled="invalidCount > 0"
          size="large"
        >
          <el-icon><Check /></el-icon>
          {{ submitting ? '提交中...' : '确认提交' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  CircleCheck,
  CircleClose,
  Document,
  Setting,
  Upload,
  ArrowLeft,
  Check
} from '@element-plus/icons-vue'

const props = defineProps({
  validCount: {
    type: Number,
    default: 0
  },
  invalidCount: {
    type: Number,
    default: 0
  },
  totalCount: {
    type: Number,
    default: 0
  },
  ruleName: {
    type: String,
    default: ''
  },
  ruleKey: {
    type: String,
    default: ''
  },
  uploadMode: {
    type: String,
    default: 'full'
  },
  operationSummary: {
    type: Object,
    default: () => ({})
  },
  submitting: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['prev-step', 'confirm'])

// 计算属性
const confirmTitle = computed(() => {
  return props.uploadMode === 'full'
    ? '确认全量数据提交'
    : '确认增量数据提交'
})

const confirmDescription = computed(() => {
  return `即将提交 ${props.validCount} 条有效数据到规则 '${props.ruleName || props.ruleKey}'`
})

// 事件处理
const handlePrevStep = () => {
  emit('prev-step')
}

const handleConfirm = () => {
  emit('confirm')
}
</script>

<style scoped>
.submission-confirm {
  padding: 20px;
}

.confirm-content {
  max-width: 800px;
  margin: 0 auto;
}

.confirm-alert {
  margin-bottom: 24px;
}

/* 数据统计卡片 */
.data-statistics {
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.stat-card:hover {
  box-shadow: var(--el-box-shadow-light);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 24px;
}

.stat-icon.valid {
  background-color: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.stat-icon.invalid {
  background-color: var(--el-color-error-light-9);
  color: var(--el-color-error);
}

.stat-icon.total {
  background-color: var(--el-color-info-light-9);
  color: var(--el-color-info);
}

.stat-icon.rule {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1;
}

.stat-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  line-height: 1.2;
  word-break: break-all;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

/* 模式信息 */
.mode-info {
  margin-bottom: 24px;
}

.mode-card {
  border: 1px solid var(--el-border-color-lighter);
}

.mode-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.mode-description {
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

.mode-description ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.mode-description li {
  margin-bottom: 4px;
}

/* 注意事项 */
.notice-section {
  margin-bottom: 32px;
}

.notice-list {
  margin: 0;
  padding-left: 20px;
}

.notice-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 操作按钮 */
.confirm-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .confirm-actions {
    flex-direction: column;
  }

  .confirm-actions .el-button {
    width: 100%;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-number {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .submission-confirm {
    padding: 16px;
  }

  .data-statistics :deep(.el-col) {
    margin-bottom: 12px;
  }
}
</style>
