/**
 * 状态机核心引擎
 * 企业级状态管理的基础设施，提供状态转换、事件处理和生命周期管理
 */

import { ref, computed, watch, onUnmounted } from 'vue'
import { AsyncStates, StateEvents } from '@/types/state'

/**
 * 默认状态机配置
 */
const DEFAULT_CONFIG = {
  initialState: AsyncStates.IDLE,

  // 状态转换规则
  transitions: {
    [AsyncStates.IDLE]: [AsyncStates.LOADING],
    [AsyncStates.LOADING]: [AsyncStates.SUCCESS, AsyncStates.ERROR],
    [AsyncStates.SUCCESS]: [AsyncStates.LOADING, AsyncStates.IDLE],
    [AsyncStates.ERROR]: [AsyncStates.RETRYING, AsyncStates.LOADING, AsyncStates.IDLE],
    [AsyncStates.RETRYING]: [AsyncStates.LOADING, AsyncStates.ERROR, AsyncStates.IDLE]
  },

  // 状态配置
  states: {
    [AsyncStates.IDLE]: {
      onEnter: () => console.debug('State: IDLE'),
      canTransitionTo: [AsyncStates.LOADING]
    },
    [AsyncStates.LOADING]: {
      onEnter: () => console.debug('State: LOADING'),
      canTransitionTo: [AsyncStates.SUCCESS, AsyncStates.ERROR]
    },
    [AsyncStates.SUCCESS]: {
      onEnter: () => console.debug('State: SUCCESS'),
      canTransitionTo: [AsyncStates.LOADING, AsyncStates.IDLE]
    },
    [AsyncStates.ERROR]: {
      onEnter: () => console.debug('State: ERROR'),
      canTransitionTo: [AsyncStates.RETRYING, AsyncStates.LOADING, AsyncStates.IDLE]
    },
    [AsyncStates.RETRYING]: {
      onEnter: () => console.debug('State: RETRYING'),
      canTransitionTo: [AsyncStates.LOADING, AsyncStates.ERROR, AsyncStates.IDLE]
    }
  }
}

/**
 * 状态机 Composable
 * @param {Object} config - 状态机配置
 * @returns {Object} 状态机实例
 */
export function useStateMachine(config = {}) {
  const finalConfig = {
    ...DEFAULT_CONFIG,
    maxHistorySize: 100, // 默认历史记录限制
    ...config
  }

  // 当前状态
  const currentState = ref(finalConfig.initialState)

  // 状态历史
  const stateHistory = ref([{
    state: finalConfig.initialState,
    timestamp: Date.now(),
    event: null
  }])

  // 事件监听器
  const listeners = ref(new Map())

  // 状态转换锁（防止并发转换）
  const transitionLock = ref(false)

  // ==================== 计算属性 ====================

  /**
   * 当前状态配置
   */
  const currentStateConfig = computed(() => {
    return finalConfig.states[currentState.value] || {}
  })

  /**
   * 可转换的状态列表
   */
  const availableTransitions = computed(() => {
    return finalConfig.transitions[currentState.value] || []
  })

  /**
   * 状态检查器
   */
  const is = computed(() => ({
    idle: currentState.value === AsyncStates.IDLE,
    loading: currentState.value === AsyncStates.LOADING,
    success: currentState.value === AsyncStates.SUCCESS,
    error: currentState.value === AsyncStates.ERROR,
    retrying: currentState.value === AsyncStates.RETRYING
  }))

  // ==================== 核心方法 ====================

  /**
   * 状态转换
   * @param {string} newState - 目标状态
   * @param {string} event - 触发事件
   * @param {Object} context - 上下文数据
   * @returns {Object} 转换结果对象
   */
  const transition = async (newState, event = null, context = {}) => {
    // 检查转换锁
    if (transitionLock.value) {
      console.warn(`State transition blocked: ${currentState.value} -> ${newState} (locked)`)
      return {
        success: false,
        error: 'State machine is currently transitioning'
      }
    }

    // 检查转换是否合法
    if (!canTransition(newState)) {
      console.warn(`Invalid state transition: ${currentState.value} -> ${newState}`)
      return {
        success: false,
        error: `Invalid transition from "${currentState.value}" to "${newState}"`
      }
    }

    try {
      transitionLock.value = true
      const oldState = currentState.value

      // 执行退出钩子
      const oldStateConfig = finalConfig.states[oldState]
      if (oldStateConfig?.onLeave) {
        const leaveResult = await oldStateConfig.onLeave(oldState, newState, context)
        if (leaveResult === false) {
          return {
            success: false,
            error: 'Leave guard rejected the transition'
          }
        }
      }

      // 执行进入钩子
      const newStateConfig = finalConfig.states[newState]
      if (newStateConfig?.onEnter) {
        const enterResult = await newStateConfig.onEnter(newState, oldState, context)
        if (enterResult === false) {
          return {
            success: false,
            error: 'Enter guard rejected the transition'
          }
        }
      }

      // 更新状态
      currentState.value = newState

      // 记录状态历史
      stateHistory.value.push({
        state: newState,
        timestamp: Date.now(),
        event,
        context,
        previousState: oldState
      })

      // 限制历史记录长度
      if (stateHistory.value.length > finalConfig.maxHistorySize) {
        stateHistory.value = stateHistory.value.slice(-Math.floor(finalConfig.maxHistorySize / 2))
      }

      // 触发离开事件
      emit(`leave:${oldState}`, {
        state: oldState,
        to: newState,
        timestamp: Date.now()
      })

      // 触发进入事件
      emit(`enter:${newState}`, {
        state: newState,
        from: oldState,
        timestamp: Date.now()
      })

      // 触发状态变化事件
      emit('stateChange', {
        from: oldState,
        to: newState,
        event,
        context,
        timestamp: Date.now()
      })

      console.debug(`State transition: ${oldState} -> ${newState}`, { event, context })
      return {
        success: true,
        from: oldState,
        to: newState
      }

    } catch (error) {
      console.error('State transition error:', error)

      // 触发错误事件
      emit('transitionError', {
        from: currentState.value,
        to: newState,
        event,
        error,
        timestamp: Date.now()
      })

      return {
        success: false,
        error: error.message
      }
    } finally {
      transitionLock.value = false
    }
  }

  /**
   * 检查是否可以转换到目标状态
   * @param {string} targetState - 目标状态
   * @returns {boolean} 是否可以转换
   */
  const canTransition = (targetState) => {
    return availableTransitions.value.includes(targetState)
  }

  /**
   * 重置状态机
   */
  const reset = () => {
    currentState.value = finalConfig.initialState
    stateHistory.value = [{
      state: finalConfig.initialState,
      timestamp: Date.now(),
      event: StateEvents.RESET
    }]

    emit('reset', {
      state: finalConfig.initialState,
      timestamp: Date.now()
    })
  }

  // ==================== 事件系统 ====================

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} handler - 事件处理器
   */
  const on = (event, handler) => {
    if (!listeners.value.has(event)) {
      listeners.value.set(event, new Set())
    }
    listeners.value.get(event).add(handler)

    // 返回取消监听的函数
    return () => off(event, handler)
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} handler - 事件处理器
   */
  const off = (event, handler) => {
    const eventListeners = listeners.value.get(event)
    if (eventListeners) {
      eventListeners.delete(handler)
      if (eventListeners.size === 0) {
        listeners.value.delete(event)
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  const emit = (event, data) => {
    const eventListeners = listeners.value.get(event)
    if (eventListeners) {
      eventListeners.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Event handler error for ${event}:`, error)
        }
      })
    }
  }

  // ==================== 便捷方法 ====================

  /**
   * 开始加载
   */
  const start = (context) => transition(AsyncStates.LOADING, StateEvents.START, context)

  /**
   * 标记成功
   */
  const success = (context) => transition(AsyncStates.SUCCESS, StateEvents.SUCCESS, context)

  /**
   * 标记错误
   */
  const error = (context) => transition(AsyncStates.ERROR, StateEvents.ERROR, context)

  /**
   * 开始重试
   */
  const retry = (context) => transition(AsyncStates.RETRYING, StateEvents.RETRY, context)

  // ==================== 调试和监控 ====================

  /**
   * 获取状态机信息
   */
  const getInfo = () => ({
    currentState: currentState.value,
    availableTransitions: availableTransitions.value,
    stateHistory: stateHistory.value,
    isLocked: transitionLock.value,
    config: finalConfig
  })

  /**
   * 导出状态历史
   */
  const exportHistory = () => {
    return JSON.stringify(stateHistory.value, null, 2)
  }

  // ==================== 生命周期 ====================

  // 监听状态变化（用于调试）
  if (process.env.NODE_ENV === 'development') {
    watch(currentState, (newState, oldState) => {
      console.debug(`[StateMachine] ${oldState} -> ${newState}`)
    })
  }

  // 清理资源
  onUnmounted(() => {
    listeners.value.clear()
  })

  // ==================== 返回接口 ====================

  // 添加测试期望的方法
  const getPreviousState = () => {
    if (stateHistory.value.length < 2) {
      return null
    }
    return stateHistory.value[stateHistory.value.length - 2].state
  }

  const rollback = async () => {
    const previousState = getPreviousState()
    if (!previousState) {
      return {
        success: false,
        error: 'No previous state to rollback to'
      }
    }

    // 临时允许回滚转换（绕过转换规则检查）
    const originalTransitions = finalConfig.transitions[currentState.value] || []
    if (!originalTransitions.includes(previousState)) {
      finalConfig.transitions[currentState.value] = [...originalTransitions, previousState]
    }

    try {
      // 执行转换到上一个状态
      const result = await transition(previousState, StateEvents.ROLLBACK)

      // 恢复原始转换规则
      finalConfig.transitions[currentState.value] = originalTransitions

      return result
    } catch (error) {
      // 恢复原始转换规则
      finalConfig.transitions[currentState.value] = originalTransitions

      return {
        success: false,
        error: error.message || 'Rollback failed'
      }
    }
  }

  const canTransitionTo = (targetState) => {
    return canTransition(targetState)
  }

  const isTransitioning = computed(() => transitionLock.value)

  return {
    // 状态 (匹配测试期望的接口)
    currentState,
    stateHistory,
    isTransitioning,
    is,

    // 状态信息
    currentStateConfig,
    availableTransitions,

    // 核心方法
    transition,
    canTransition,
    canTransitionTo,
    reset,
    getPreviousState,
    rollback,

    // 事件系统
    on,
    off,
    emit,

    // 便捷方法
    start,
    success,
    error,
    retry,

    // 调试和监控
    getInfo,
    exportHistory
  }
}
