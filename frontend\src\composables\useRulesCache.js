/**
 * 智能缓存管理组合式函数
 * 提供多级缓存、智能失效、性能监控等功能
 */

import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRulesStore } from '@/stores/rules'
import { useRuleDetailsStore } from '@/stores/ruleDetails'

/**
 * 缓存策略枚举
 */
export const CacheStrategy = {
  MEMORY_ONLY: 'memory_only',
  SESSION_STORAGE: 'session_storage',
  LOCAL_STORAGE: 'local_storage',
  HYBRID: 'hybrid'
}

/**
 * 缓存优先级枚举
 */
export const CachePriority = {
  LOW: 1,
  NORMAL: 2,
  HIGH: 3,
  CRITICAL: 4
}

/**
 * 智能缓存管理
 * @param {Object} options - 配置选项
 * @returns {Object} 缓存管理对象
 */
export function useRulesCache(options = {}) {
  const {
    strategy = CacheStrategy.HYBRID,
    maxMemorySize = 100,
    maxStorageSize = 500,
    defaultTTL = 10 * 60 * 1000, // 10分钟
    compressionEnabled = true,
    autoCleanup = true,
    cleanupInterval = 5 * 60 * 1000 // 5分钟
  } = options

  // Store 实例
  const rulesStore = useRulesStore()
  const ruleDetailsStore = useRuleDetailsStore()

  // 缓存状态
  const cacheStats = ref({
    memoryHits: 0,
    memoryMisses: 0,
    storageHits: 0,
    storageMisses: 0,
    totalRequests: 0,
    lastCleanup: Date.now(),
    compressionRatio: 0
  })

  // 内存缓存
  const memoryCache = ref(new Map())
  
  // 缓存元数据
  const cacheMetadata = ref(new Map())

  // 清理定时器
  let cleanupTimer = null

  // 计算属性
  const memoryHitRate = computed(() => {
    const total = cacheStats.value.memoryHits + cacheStats.value.memoryMisses
    return total > 0 ? (cacheStats.value.memoryHits / total * 100).toFixed(2) : '100.00'
  })

  const storageHitRate = computed(() => {
    const total = cacheStats.value.storageHits + cacheStats.value.storageMisses
    return total > 0 ? (cacheStats.value.storageHits / total * 100).toFixed(2) : '100.00'
  })

  const overallHitRate = computed(() => {
    const totalHits = cacheStats.value.memoryHits + cacheStats.value.storageHits
    const totalRequests = cacheStats.value.totalRequests
    return totalRequests > 0 ? (totalHits / totalRequests * 100).toFixed(2) : '100.00'
  })

  const cacheUtilization = computed(() => ({
    memory: {
      used: memoryCache.value.size,
      max: maxMemorySize,
      percentage: (memoryCache.value.size / maxMemorySize * 100).toFixed(2)
    },
    storage: {
      used: getStorageCacheSize(),
      max: maxStorageSize,
      percentage: (getStorageCacheSize() / maxStorageSize * 100).toFixed(2)
    }
  }))

  const performanceMetrics = computed(() => ({
    hitRates: {
      memory: memoryHitRate.value,
      storage: storageHitRate.value,
      overall: overallHitRate.value
    },
    utilization: cacheUtilization.value,
    stats: cacheStats.value,
    health: getHealthScore()
  }))

  // 工具方法
  const generateCacheKey = (type, identifier, params = {}) => {
    const paramStr = Object.keys(params).length > 0 ? 
      '_' + btoa(JSON.stringify(params)).replace(/[^a-zA-Z0-9]/g, '') : ''
    return `${type}_${identifier}${paramStr}`
  }

  const compressData = (data) => {
    if (!compressionEnabled) return data
    
    try {
      const jsonStr = JSON.stringify(data)
      // 简单的压缩：移除空格和换行
      const compressed = jsonStr.replace(/\s+/g, ' ').trim()
      
      // 更新压缩比率
      const ratio = compressed.length / jsonStr.length
      cacheStats.value.compressionRatio = 
        (cacheStats.value.compressionRatio + ratio) / 2
      
      return compressed
    } catch (error) {
      console.warn('数据压缩失败:', error)
      return data
    }
  }

  const decompressData = (compressedData) => {
    if (!compressionEnabled) return compressedData
    
    try {
      return typeof compressedData === 'string' ? 
        JSON.parse(compressedData) : compressedData
    } catch (error) {
      console.warn('数据解压失败:', error)
      return compressedData
    }
  }

  const getStorageCacheSize = () => {
    try {
      let size = 0
      for (let key in sessionStorage) {
        if (key.startsWith('rules_cache_')) {
          size++
        }
      }
      return size
    } catch (error) {
      return 0
    }
  }

  const getHealthScore = () => {
    const hitRate = parseFloat(overallHitRate.value)
    const memoryUtil = parseFloat(cacheUtilization.value.memory.percentage)
    const storageUtil = parseFloat(cacheUtilization.value.storage.percentage)
    
    let score = hitRate
    
    // 内存使用率过高扣分
    if (memoryUtil > 90) score -= 10
    else if (memoryUtil > 80) score -= 5
    
    // 存储使用率过高扣分
    if (storageUtil > 90) score -= 10
    else if (storageUtil > 80) score -= 5
    
    return Math.max(0, Math.min(100, score))
  }

  // 缓存操作方法
  const set = (key, data, options = {}) => {
    const {
      ttl = defaultTTL,
      priority = CachePriority.NORMAL,
      useStorage = strategy === CacheStrategy.HYBRID || strategy === CacheStrategy.SESSION_STORAGE
    } = options

    const cacheItem = {
      data: compressData(data),
      timestamp: Date.now(),
      ttl,
      priority,
      accessCount: 0,
      lastAccess: Date.now()
    }

    // 存储到内存缓存
    if (strategy !== CacheStrategy.SESSION_STORAGE) {
      // 检查内存缓存大小
      if (memoryCache.value.size >= maxMemorySize) {
        evictLeastUsed('memory')
      }
      
      memoryCache.value.set(key, cacheItem)
      cacheMetadata.value.set(key, { location: 'memory', ...cacheItem })
    }

    // 存储到会话存储
    if (useStorage) {
      try {
        if (getStorageCacheSize() >= maxStorageSize) {
          evictLeastUsed('storage')
        }
        
        sessionStorage.setItem(`rules_cache_${key}`, JSON.stringify(cacheItem))
        cacheMetadata.value.set(key, { location: 'storage', ...cacheItem })
      } catch (error) {
        console.warn('存储缓存失败:', error)
      }
    }
  }

  const get = (key) => {
    cacheStats.value.totalRequests++

    // 首先检查内存缓存
    if (memoryCache.value.has(key)) {
      const item = memoryCache.value.get(key)
      
      // 检查是否过期
      if (Date.now() - item.timestamp > item.ttl) {
        memoryCache.value.delete(key)
        cacheMetadata.value.delete(key)
        cacheStats.value.memoryMisses++
      } else {
        // 更新访问信息
        item.accessCount++
        item.lastAccess = Date.now()
        cacheStats.value.memoryHits++
        return decompressData(item.data)
      }
    } else {
      cacheStats.value.memoryMisses++
    }

    // 检查会话存储
    try {
      const storageItem = sessionStorage.getItem(`rules_cache_${key}`)
      if (storageItem) {
        const item = JSON.parse(storageItem)
        
        // 检查是否过期
        if (Date.now() - item.timestamp > item.ttl) {
          sessionStorage.removeItem(`rules_cache_${key}`)
          cacheMetadata.value.delete(key)
          cacheStats.value.storageMisses++
        } else {
          // 提升到内存缓存
          if (strategy === CacheStrategy.HYBRID) {
            memoryCache.value.set(key, item)
          }
          
          cacheStats.value.storageHits++
          return decompressData(item.data)
        }
      } else {
        cacheStats.value.storageMisses++
      }
    } catch (error) {
      console.warn('读取存储缓存失败:', error)
      cacheStats.value.storageMisses++
    }

    return null
  }

  const has = (key) => {
    return memoryCache.value.has(key) || 
           sessionStorage.getItem(`rules_cache_${key}`) !== null
  }

  const remove = (key) => {
    memoryCache.value.delete(key)
    sessionStorage.removeItem(`rules_cache_${key}`)
    cacheMetadata.value.delete(key)
  }

  const clear = (type = 'all') => {
    if (type === 'all' || type === 'memory') {
      memoryCache.value.clear()
    }
    
    if (type === 'all' || type === 'storage') {
      try {
        const keysToRemove = []
        for (let key in sessionStorage) {
          if (key.startsWith('rules_cache_')) {
            keysToRemove.push(key)
          }
        }
        keysToRemove.forEach(key => sessionStorage.removeItem(key))
      } catch (error) {
        console.warn('清理存储缓存失败:', error)
      }
    }
    
    if (type === 'all') {
      cacheMetadata.value.clear()
      // 重置统计
      cacheStats.value = {
        memoryHits: 0,
        memoryMisses: 0,
        storageHits: 0,
        storageMisses: 0,
        totalRequests: 0,
        lastCleanup: Date.now(),
        compressionRatio: 0
      }
    }
  }

  const evictLeastUsed = (location = 'memory') => {
    if (location === 'memory') {
      // 找到最少使用的项目
      let leastUsedKey = null
      let leastUsedItem = null
      
      for (const [key, item] of memoryCache.value.entries()) {
        if (!leastUsedItem || 
            item.accessCount < leastUsedItem.accessCount ||
            (item.accessCount === leastUsedItem.accessCount && 
             item.lastAccess < leastUsedItem.lastAccess)) {
          leastUsedKey = key
          leastUsedItem = item
        }
      }
      
      if (leastUsedKey) {
        memoryCache.value.delete(leastUsedKey)
        cacheMetadata.value.delete(leastUsedKey)
      }
    } else if (location === 'storage') {
      // 清理最旧的存储项目
      try {
        const storageKeys = []
        for (let key in sessionStorage) {
          if (key.startsWith('rules_cache_')) {
            const item = JSON.parse(sessionStorage.getItem(key))
            storageKeys.push({ key, timestamp: item.timestamp })
          }
        }
        
        storageKeys.sort((a, b) => a.timestamp - b.timestamp)
        if (storageKeys.length > 0) {
          sessionStorage.removeItem(storageKeys[0].key)
        }
      } catch (error) {
        console.warn('清理存储缓存失败:', error)
      }
    }
  }

  const cleanup = () => {
    const now = Date.now()
    
    // 清理过期的内存缓存
    for (const [key, item] of memoryCache.value.entries()) {
      if (now - item.timestamp > item.ttl) {
        memoryCache.value.delete(key)
        cacheMetadata.value.delete(key)
      }
    }
    
    // 清理过期的存储缓存
    try {
      const keysToRemove = []
      for (let key in sessionStorage) {
        if (key.startsWith('rules_cache_')) {
          const item = JSON.parse(sessionStorage.getItem(key))
          if (now - item.timestamp > item.ttl) {
            keysToRemove.push(key)
          }
        }
      }
      keysToRemove.forEach(key => sessionStorage.removeItem(key))
    } catch (error) {
      console.warn('清理存储缓存失败:', error)
    }
    
    cacheStats.value.lastCleanup = now
  }

  // 生命周期管理
  onMounted(() => {
    if (autoCleanup) {
      cleanupTimer = setInterval(cleanup, cleanupInterval)
    }
  })

  onUnmounted(() => {
    if (cleanupTimer) {
      clearInterval(cleanupTimer)
    }
  })

  return {
    // 状态
    cacheStats,
    performanceMetrics,
    
    // 方法
    set,
    get,
    has,
    remove,
    clear,
    cleanup,
    generateCacheKey,
    
    // 工具方法
    evictLeastUsed,
    compressData,
    decompressData,
    
    // 配置
    strategy,
    maxMemorySize,
    maxStorageSize,
    defaultTTL
  }
}
