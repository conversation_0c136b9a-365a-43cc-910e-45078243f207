/**
 * 动态校验引擎单元测试
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { DynamicValidationEngine } from '../dynamicValidationEngine'
import { ValidationRuleType } from '../validationTypes'

// Mock fetch
Object.defineProperty(globalThis, 'fetch', {
  value: vi.fn(),
  writable: true
})

describe('DynamicValidationEngine', () => {
  let engine: DynamicValidationEngine
  
  beforeEach(() => {
    engine = new DynamicValidationEngine({
      cache_enabled: true,
      cache_ttl_minutes: 30
    })
    
    // 重置 fetch mock
    vi.clearAllMocks()
  })

  afterEach(() => {
    engine.clearCache()
  })

  describe('getValidationConfig', () => {
    it('应该成功获取校验配置', async () => {
      const mockResponse = {
        success: true,
        data: {
          fields: {
            rule_name: {
              field_name: 'rule_name',
              chinese_name: '规则名称',
              is_required: true,
              data_type: 'string',
              rules: [
                {
                  type: 'required',
                  message: '规则名称不能为空',
                  priority: 0
                },
                {
                  type: 'max_length',
                  value: 255,
                  message: '规则名称不能超过255个字符',
                  priority: 1
                }
              ]
            }
          }
        }
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const config = await engine.getValidationConfig('test_rule')
      
      expect(config).toHaveLength(1)
      expect(config[0].field_name).toBe('rule_name')
      expect(config[0].chinese_name).toBe('规则名称')
      expect(config[0].is_required).toBe(true)
      expect(config[0].rules).toHaveLength(2)
    })

    it('应该处理API错误', async () => {
      ;(fetch as any).mockResolvedValueOnce({
        ok: false,
        statusText: 'Not Found'
      })

      const config = await engine.getValidationConfig('nonexistent_rule')
      expect(config).toEqual([])
    })

    it('应该使用缓存', async () => {
      const mockResponse = {
        success: true,
        data: { fields: {} }
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      // 第一次调用
      await engine.getValidationConfig('test_rule')
      
      // 第二次调用应该使用缓存
      await engine.getValidationConfig('test_rule')
      
      expect(fetch).toHaveBeenCalledTimes(1)
    })
  })

  describe('validateField', () => {
    beforeEach(() => {
      // Mock getValidationConfig
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([
        {
          field_name: 'rule_name',
          chinese_name: '规则名称',
          rules: [
            {
              field_name: 'rule_name',
              chinese_name: '规则名称',
              rule_type: ValidationRuleType.REQUIRED,
              rule_value: true,
              error_message: '规则名称不能为空',
              is_required: true,
              priority: 0
            },
            {
              field_name: 'rule_name',
              chinese_name: '规则名称',
              rule_type: ValidationRuleType.MAX_LENGTH,
              rule_value: 255,
              error_message: '规则名称不能超过255个字符',
              is_required: false,
              priority: 1
            }
          ],
          element_rules: [],
          is_required: true,
          data_type: 'string'
        }
      ])
    })

    it('应该通过必填字段校验', async () => {
      const result = await engine.validateField('rule_name', '测试规则', 'test_rule')
      
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.field_count).toBe(1)
      expect(result.validated_fields).toContain('rule_name')
    })

    it('应该检测必填字段为空', async () => {
      const result = await engine.validateField('rule_name', '', 'test_rule')
      
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].error_code).toBe('REQUIRED_FIELD_MISSING')
      expect(result.errors[0].error_message).toBe('规则名称不能为空')
    })

    it('应该检测字符串长度超限', async () => {
      const longString = 'a'.repeat(300)
      const result = await engine.validateField('rule_name', longString, 'test_rule')
      
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].error_code).toBe('MAX_LENGTH_VIOLATION')
    })

    it('应该处理不存在的字段', async () => {
      const result = await engine.validateField('nonexistent_field', 'value', 'test_rule')
      
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('validateForm', () => {
    beforeEach(() => {
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([
        {
          field_name: 'rule_name',
          chinese_name: '规则名称',
          rules: [
            {
              field_name: 'rule_name',
              chinese_name: '规则名称',
              rule_type: ValidationRuleType.REQUIRED,
              rule_value: true,
              error_message: '规则名称不能为空',
              is_required: true,
              priority: 0
            }
          ],
          element_rules: [],
          is_required: true,
          data_type: 'string'
        },
        {
          field_name: 'level1',
          chinese_name: '一级错误类型',
          rules: [
            {
              field_name: 'level1',
              chinese_name: '一级错误类型',
              rule_type: ValidationRuleType.REQUIRED,
              rule_value: true,
              error_message: '一级错误类型不能为空',
              is_required: true,
              priority: 0
            }
          ],
          element_rules: [],
          is_required: true,
          data_type: 'string'
        }
      ])
    })

    it('应该通过完整表单校验', async () => {
      const formData = {
        rule_name: '测试规则',
        level1: '用药安全'
      }
      
      const result = await engine.validateForm(formData, 'test_rule')
      
      expect(result.valid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.field_count).toBe(2)
      expect(result.validated_fields).toContain('rule_name')
      expect(result.validated_fields).toContain('level1')
    })

    it('应该检测多个字段错误', async () => {
      const formData = {
        rule_name: '',
        level1: ''
      }
      
      const result = await engine.validateForm(formData, 'test_rule')
      
      expect(result.valid).toBe(false)
      expect(result.errors).toHaveLength(2)
      expect(result.errors.some(e => e.field_name === 'rule_name')).toBe(true)
      expect(result.errors.some(e => e.field_name === 'level1')).toBe(true)
    })
  })

  describe('校验规则类型', () => {
    it('应该校验邮箱格式', async () => {
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([
        {
          field_name: 'email',
          chinese_name: '邮箱',
          rules: [
            {
              field_name: 'email',
              chinese_name: '邮箱',
              rule_type: ValidationRuleType.EMAIL,
              rule_value: true,
              error_message: '邮箱格式不正确',
              is_required: false,
              priority: 0
            }
          ],
          element_rules: [],
          is_required: false,
          data_type: 'string'
        }
      ])

      // 有效邮箱
      let result = await engine.validateField('email', '<EMAIL>', 'test_rule')
      expect(result.valid).toBe(true)

      // 无效邮箱
      result = await engine.validateField('email', 'invalid-email', 'test_rule')
      expect(result.valid).toBe(false)
      expect(result.errors[0].error_code).toBe('INVALID_EMAIL_FORMAT')
    })

    it('应该校验整数类型', async () => {
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([
        {
          field_name: 'age',
          chinese_name: '年龄',
          rules: [
            {
              field_name: 'age',
              chinese_name: '年龄',
              rule_type: ValidationRuleType.INTEGER,
              rule_value: true,
              error_message: '年龄必须是整数',
              is_required: false,
              priority: 0
            }
          ],
          element_rules: [],
          is_required: false,
          data_type: 'integer'
        }
      ])

      // 有效整数
      let result = await engine.validateField('age', '25', 'test_rule')
      expect(result.valid).toBe(true)

      // 无效整数
      result = await engine.validateField('age', '25.5', 'test_rule')
      expect(result.valid).toBe(false)
      expect(result.errors[0].error_code).toBe('INVALID_INTEGER')
    })

    it('应该校验数值范围', async () => {
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([
        {
          field_name: 'score',
          chinese_name: '分数',
          rules: [
            {
              field_name: 'score',
              chinese_name: '分数',
              rule_type: ValidationRuleType.MIN_VALUE,
              rule_value: 0,
              error_message: '分数不能小于0',
              is_required: false,
              priority: 0
            },
            {
              field_name: 'score',
              chinese_name: '分数',
              rule_type: ValidationRuleType.MAX_VALUE,
              rule_value: 100,
              error_message: '分数不能大于100',
              is_required: false,
              priority: 1
            }
          ],
          element_rules: [],
          is_required: false,
          data_type: 'number'
        }
      ])

      // 有效范围
      let result = await engine.validateField('score', '85', 'test_rule')
      expect(result.valid).toBe(true)

      // 小于最小值
      result = await engine.validateField('score', '-10', 'test_rule')
      expect(result.valid).toBe(false)
      expect(result.errors[0].error_code).toBe('MIN_VALUE_VIOLATION')

      // 大于最大值
      result = await engine.validateField('score', '150', 'test_rule')
      expect(result.valid).toBe(false)
      expect(result.errors[0].error_code).toBe('MAX_VALUE_VIOLATION')
    })
  })

  describe('性能指标', () => {
    it('应该记录性能指标', async () => {
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([])
      
      await engine.validateField('test_field', 'value', 'test_rule')
      
      const metrics = engine.getPerformanceMetrics()
      expect(metrics.total_validations).toBe(1)
      expect(metrics.average_duration).toBeGreaterThan(0)
    })

    it('应该重置性能指标', async () => {
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([])
      
      await engine.validateField('test_field', 'value', 'test_rule')
      engine.resetPerformanceMetrics()
      
      const metrics = engine.getPerformanceMetrics()
      expect(metrics.total_validations).toBe(0)
      expect(metrics.average_duration).toBe(0)
    })
  })

  describe('事件系统', () => {
    it('应该触发字段校验事件', async () => {
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([])
      
      const eventListener = vi.fn()
      engine.addEventListener('field_validated', eventListener)
      
      await engine.validateField('test_field', 'value', 'test_rule')
      
      expect(eventListener).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'field_validated',
          field_name: 'test_field'
        })
      )
    })

    it('应该移除事件监听器', async () => {
      vi.spyOn(engine, 'getValidationConfig').mockResolvedValue([])
      
      const eventListener = vi.fn()
      engine.addEventListener('field_validated', eventListener)
      engine.removeEventListener('field_validated', eventListener)
      
      await engine.validateField('test_field', 'value', 'test_rule')
      
      expect(eventListener).not.toHaveBeenCalled()
    })
  })
})
